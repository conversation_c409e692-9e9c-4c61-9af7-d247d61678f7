"""
LangGraph多Agent系统测试

测试LangGraph多Agent系统的功能和性能，验证协作效果
"""

import time
import base64
import io
import asyncio
import json
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import cv2
from typing import Dict, Any

from diagram_analysis.langgraph import get_workflow_instance, create_workflow
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("test_langgraph", CONFIG)


def create_test_flowchart() -> str:
    """创建测试流程图"""
    try:
        # 创建一个简单的流程图
        img = Image.new('RGB', (800, 600), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制流程图元素
        # 开始节点
        draw.ellipse([100, 50, 200, 100], outline='black', width=2)
        draw.text((130, 70), "开始", fill='black')
        
        # 处理节点
        draw.rectangle([100, 150, 200, 200], outline='black', width=2)
        draw.text((120, 170), "处理数据", fill='black')
        
        # 决策节点
        draw.polygon([(150, 250), (200, 300), (150, 350), (100, 300)], outline='black', width=2)
        draw.text((130, 295), "判断", fill='black')
        
        # 结束节点
        draw.ellipse([100, 400, 200, 450], outline='black', width=2)
        draw.text((130, 420), "结束", fill='black')
        
        # 连接线
        draw.line([(150, 100), (150, 150)], fill='black', width=2)
        draw.line([(150, 200), (150, 250)], fill='black', width=2)
        draw.line([(150, 350), (150, 400)], fill='black', width=2)
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
        
    except Exception as e:
        logger.error(f"创建测试流程图失败: {str(e)}")
        return ""


def create_test_sequence_diagram() -> str:
    """创建测试时序图"""
    try:
        # 创建一个简单的时序图
        img = Image.new('RGB', (800, 600), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制参与者
        draw.text((100, 50), "用户", fill='black')
        draw.text((300, 50), "系统", fill='black')
        draw.text((500, 50), "数据库", fill='black')
        
        # 绘制生命线
        draw.line([(120, 70), (120, 500)], fill='black', width=1)
        draw.line([(320, 70), (320, 500)], fill='black', width=1)
        draw.line([(520, 70), (520, 500)], fill='black', width=1)
        
        # 绘制消息
        draw.line([(120, 150), (320, 150)], fill='black', width=2)
        draw.text((180, 130), "登录请求", fill='black')
        
        draw.line([(320, 200), (520, 200)], fill='black', width=2)
        draw.text((380, 180), "查询用户", fill='black')
        
        draw.line([(520, 250), (320, 250)], fill='black', width=2)
        draw.text((380, 230), "返回结果", fill='black')
        
        draw.line([(320, 300), (120, 300)], fill='black', width=2)
        draw.text((180, 280), "登录成功", fill='black')
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
        
    except Exception as e:
        logger.error(f"创建测试时序图失败: {str(e)}")
        return ""


def test_single_workflow():
    """测试单个工作流执行"""
    logger.info("开始测试单个工作流执行")
    
    try:
        # 创建测试图像
        test_image = create_test_flowchart()
        if not test_image:
            raise Exception("测试图像创建失败")
        
        # 获取工作流实例
        workflow = get_workflow_instance()
        
        # 构建请求参数
        request_params = {
            'output_format': 'mermaid',
            'diagram_type': 'auto'
        }
        
        # 执行工作流
        start_time = time.time()
        result = workflow.run_sync(test_image, request_params)
        duration = time.time() - start_time
        
        # 验证结果
        logger.info(f"工作流执行完成，耗时: {duration:.2f}秒")
        logger.info(f"执行结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 检查结果完整性
        assert 'success' in result, "结果中缺少success字段"
        assert 'diagram_type' in result, "结果中缺少diagram_type字段"
        
        if result['success']:
            assert 'mermaid_code' in result, "成功结果中缺少mermaid_code字段"
            logger.info("✓ 单个工作流测试通过")
        else:
            logger.warning(f"工作流执行失败: {result.get('error', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"单个工作流测试失败: {str(e)}")
        return None


async def test_async_workflow():
    """测试异步工作流执行"""
    logger.info("开始测试异步工作流执行")
    
    try:
        # 创建测试图像
        test_image = create_test_sequence_diagram()
        if not test_image:
            raise Exception("测试图像创建失败")
        
        # 获取工作流实例
        workflow = get_workflow_instance()
        
        # 构建请求参数
        request_params = {
            'output_format': 'all',
            'diagram_type': 'sequence_diagram'
        }
        
        # 执行异步工作流
        start_time = time.time()
        result = await workflow.run_async(test_image, request_params)
        duration = time.time() - start_time
        
        # 验证结果
        logger.info(f"异步工作流执行完成，耗时: {duration:.2f}秒")
        logger.info(f"执行结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 检查结果完整性
        assert 'success' in result, "结果中缺少success字段"
        assert 'diagram_type' in result, "结果中缺少diagram_type字段"
        
        if result['success']:
            logger.info("✓ 异步工作流测试通过")
        else:
            logger.warning(f"异步工作流执行失败: {result.get('error', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"异步工作流测试失败: {str(e)}")
        return None


def test_multiple_workflows():
    """测试多个工作流并发执行"""
    logger.info("开始测试多个工作流并发执行")
    
    try:
        # 创建多个测试图像
        test_images = [
            create_test_flowchart(),
            create_test_sequence_diagram(),
            create_test_flowchart()
        ]
        
        # 过滤掉创建失败的图像
        test_images = [img for img in test_images if img]
        if not test_images:
            raise Exception("所有测试图像创建失败")
        
        # 执行多个工作流
        results = []
        start_time = time.time()
        
        for i, test_image in enumerate(test_images):
            # 创建新的工作流实例（避免状态冲突）
            workflow = create_workflow()
            
            request_params = {
                'output_format': 'mermaid',
                'diagram_type': 'auto'
            }
            
            logger.info(f"执行第{i+1}个工作流")
            result = workflow.run_sync(test_image, request_params, f"test_session_{i}")
            results.append(result)
        
        duration = time.time() - start_time
        
        # 验证结果
        logger.info(f"多个工作流执行完成，总耗时: {duration:.2f}秒")
        
        success_count = sum(1 for result in results if result.get('success', False))
        logger.info(f"成功执行: {success_count}/{len(results)} 个工作流")
        
        if success_count > 0:
            logger.info("✓ 多工作流测试通过")
        else:
            logger.warning("所有工作流执行失败")
            
        return results
        
    except Exception as e:
        logger.error(f"多工作流测试失败: {str(e)}")
        return []


def test_error_handling():
    """测试错误处理"""
    logger.info("开始测试错误处理")
    
    try:
        # 获取工作流实例
        workflow = get_workflow_instance()
        
        # 测试无效图像
        request_params = {
            'output_format': 'mermaid',
            'diagram_type': 'auto'
        }
        
        # 使用无效的base64数据
        invalid_image = "invalid_base64_data"
        
        start_time = time.time()
        result = workflow.run_sync(invalid_image, request_params, "error_test_session")
        duration = time.time() - start_time
        
        logger.info(f"错误处理测试完成，耗时: {duration:.2f}秒")
        logger.info(f"错误处理结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证错误处理
        assert 'success' in result, "结果中缺少success字段"
        assert not result['success'], "应该返回失败结果"
        assert 'error' in result, "失败结果中缺少error字段"
        
        logger.info("✓ 错误处理测试通过")
        return result
        
    except Exception as e:
        logger.error(f"错误处理测试失败: {str(e)}")
        return None


def test_performance():
    """测试性能指标"""
    logger.info("开始测试性能指标")
    
    try:
        # 创建测试图像
        test_image = create_test_flowchart()
        if not test_image:
            raise Exception("测试图像创建失败")
        
        # 执行多次测试
        execution_times = []
        success_count = 0
        
        for i in range(3):  # 执行3次测试
            workflow = create_workflow()
            request_params = {
                'output_format': 'mermaid',
                'diagram_type': 'auto'
            }
            
            start_time = time.time()
            result = workflow.run_sync(test_image, request_params, f"perf_test_{i}")
            duration = time.time() - start_time
            
            execution_times.append(duration)
            if result.get('success', False):
                success_count += 1
            
            logger.info(f"第{i+1}次执行耗时: {duration:.2f}秒")
        
        # 计算性能指标
        avg_time = sum(execution_times) / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        success_rate = success_count / len(execution_times)
        
        logger.info(f"性能测试结果:")
        logger.info(f"  平均执行时间: {avg_time:.2f}秒")
        logger.info(f"  最短执行时间: {min_time:.2f}秒")
        logger.info(f"  最长执行时间: {max_time:.2f}秒")
        logger.info(f"  成功率: {success_rate:.2%}")
        
        # 性能要求验证
        if avg_time < 60:  # 平均执行时间小于60秒
            logger.info("✓ 性能测试通过")
        else:
            logger.warning("性能测试未达到预期")
            
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'success_rate': success_rate
        }
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
        return None


def main():
    """主测试函数"""
    logger.info("开始LangGraph多Agent系统测试")
    
    test_results = {}
    
    # 1. 测试单个工作流
    test_results['single_workflow'] = test_single_workflow()
    
    # 2. 测试异步工作流
    try:
        test_results['async_workflow'] = asyncio.run(test_async_workflow())
    except Exception as e:
        logger.error(f"异步测试失败: {str(e)}")
        test_results['async_workflow'] = None
    
    # 3. 测试多个工作流
    test_results['multiple_workflows'] = test_multiple_workflows()
    
    # 4. 测试错误处理
    test_results['error_handling'] = test_error_handling()
    
    # 5. 测试性能
    test_results['performance'] = test_performance()
    
    # 汇总测试结果
    logger.info("=" * 50)
    logger.info("测试结果汇总:")
    
    for test_name, result in test_results.items():
        if result is not None:
            if isinstance(result, dict) and 'success' in result:
                status = "✓ 通过" if result['success'] else "✗ 失败"
            elif isinstance(result, list):
                success_count = sum(1 for r in result if r and r.get('success', False))
                status = f"✓ 通过 ({success_count}/{len(result)})"
            else:
                status = "✓ 通过"
        else:
            status = "✗ 失败"
        
        logger.info(f"  {test_name}: {status}")
    
    logger.info("LangGraph多Agent系统测试完成")


if __name__ == "__main__":
    main()
