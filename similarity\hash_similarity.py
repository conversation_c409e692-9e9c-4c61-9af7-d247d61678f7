"""
感知哈希相似度计算模块

实现pHash、aHash、dHash等算法进行快速图像相似度初筛
"""

import cv2
import numpy as np
from typing import Dict, Any, Tuple
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("hash_similarity", CONFIG)


class HashSimilarityCalculator:
    """感知哈希相似度计算器"""
    
    def __init__(self, config: dict = None):
        """
        初始化感知哈希相似度计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('hash', {})
        self.hash_size = self.config.get('hash_size', 8)
        self.dct_size = self.config.get('dct_size', 32)
        
    def calculate_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算两张图像的哈希相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: 包含各种哈希相似度指标的字典
        """
        try:
            # 确保图像为灰度图
            if len(image1.shape) == 3:
                image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
            if len(image2.shape) == 3:
                image2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
            
            # 计算各种哈希值
            phash1, phash2 = self._calculate_phash(image1), self._calculate_phash(image2)
            ahash1, ahash2 = self._calculate_ahash(image1), self._calculate_ahash(image2)
            dhash1, dhash2 = self._calculate_dhash(image1), self._calculate_dhash(image2)
            
            # 计算汉明距离和相似度
            phash_distance = self._hamming_distance(phash1, phash2)
            ahash_distance = self._hamming_distance(ahash1, ahash2)
            dhash_distance = self._hamming_distance(dhash1, dhash2)
            
            # 转换为相似度分数 (0-1)
            hash_bits = self.hash_size * self.hash_size
            phash_similarity = 1.0 - (phash_distance / hash_bits)
            ahash_similarity = 1.0 - (ahash_distance / hash_bits)
            dhash_similarity = 1.0 - (dhash_distance / hash_bits)
            
            results = {
                'phash_similarity': float(phash_similarity),
                'ahash_similarity': float(ahash_similarity),
                'dhash_similarity': float(dhash_similarity),
                'phash_distance': int(phash_distance),
                'ahash_distance': int(ahash_distance),
                'dhash_distance': int(dhash_distance),
                'hash_values': {
                    'phash1': phash1,
                    'phash2': phash2,
                    'ahash1': ahash1,
                    'ahash2': ahash2,
                    'dhash1': dhash1,
                    'dhash2': dhash2
                }
            }
            
            logger.debug(f"哈希相似度计算完成: pHash={phash_similarity:.4f}, aHash={ahash_similarity:.4f}, dHash={dhash_similarity:.4f}")
            return results
            
        except Exception as e:
            logger.error(f"哈希相似度计算失败: {str(e)}")
            raise
    
    def _calculate_phash(self, image: np.ndarray) -> str:
        """
        计算感知哈希(pHash)
        
        Args:
            image: 输入图像
            
        Returns:
            str: 64位二进制哈希字符串
        """
        # 1. 调整图像大小到32x32
        resized = cv2.resize(image, (self.dct_size, self.dct_size), interpolation=cv2.INTER_AREA)
        
        # 2. 转换为浮点数
        resized = resized.astype(np.float32)
        
        # 3. 计算DCT变换
        dct = cv2.dct(resized)
        
        # 4. 提取左上角8x8区域
        dct_low = dct[:self.hash_size, :self.hash_size]
        
        # 5. 计算均值（排除DC分量）
        mean_val = np.mean(dct_low[1:, 1:])
        
        # 6. 生成哈希值
        hash_bits = []
        for i in range(self.hash_size):
            for j in range(self.hash_size):
                if i == 0 and j == 0:
                    continue  # 跳过DC分量
                hash_bits.append('1' if dct_low[i, j] > mean_val else '0')
        
        return ''.join(hash_bits)
    
    def _calculate_ahash(self, image: np.ndarray) -> str:
        """
        计算平均哈希(aHash)
        
        Args:
            image: 输入图像
            
        Returns:
            str: 64位二进制哈希字符串
        """
        # 1. 调整图像大小到8x8
        resized = cv2.resize(image, (self.hash_size, self.hash_size), interpolation=cv2.INTER_AREA)
        
        # 2. 计算平均值
        mean_val = np.mean(resized)
        
        # 3. 生成哈希值
        hash_bits = []
        for i in range(self.hash_size):
            for j in range(self.hash_size):
                hash_bits.append('1' if resized[i, j] > mean_val else '0')
        
        return ''.join(hash_bits)
    
    def _calculate_dhash(self, image: np.ndarray) -> str:
        """
        计算差异哈希(dHash)
        
        Args:
            image: 输入图像
            
        Returns:
            str: 64位二进制哈希字符串
        """
        # 1. 调整图像大小到9x8 (多一列用于计算差异)
        resized = cv2.resize(image, (self.hash_size + 1, self.hash_size), interpolation=cv2.INTER_AREA)
        
        # 2. 计算水平差异
        hash_bits = []
        for i in range(self.hash_size):
            for j in range(self.hash_size):
                # 比较相邻像素
                hash_bits.append('1' if resized[i, j] > resized[i, j + 1] else '0')
        
        return ''.join(hash_bits)
    
    def _hamming_distance(self, hash1: str, hash2: str) -> int:
        """
        计算两个哈希值的汉明距离
        
        Args:
            hash1: 第一个哈希值
            hash2: 第二个哈希值
            
        Returns:
            int: 汉明距离
        """
        if len(hash1) != len(hash2):
            raise ValueError(f"哈希长度不匹配: {len(hash1)} vs {len(hash2)}")
        
        distance = sum(c1 != c2 for c1, c2 in zip(hash1, hash2))
        return distance
    
    def get_similarity_score(self, results: Dict[str, Any]) -> float:
        """
        根据各项哈希指标计算综合相似度分数
        
        Args:
            results: 哈希相似度计算结果
            
        Returns:
            float: 综合相似度分数 (0-1)
        """
        # 权重配置
        weights = self.config.get('weights', {
            'phash': 0.5,
            'ahash': 0.3,
            'dhash': 0.2
        })
        
        # 计算加权平均
        score = (
            weights['phash'] * results['phash_similarity'] +
            weights['ahash'] * results['ahash_similarity'] +
            weights['dhash'] * results['dhash_similarity']
        )
        
        return max(0.0, min(1.0, score))  # 确保分数在[0,1]范围内
    
    def is_similar_fast(self, image1: np.ndarray, image2: np.ndarray, threshold: float = 0.8) -> bool:
        """
        快速判断两张图像是否相似（用于初筛）
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            threshold: 相似度阈值
            
        Returns:
            bool: 是否相似
        """
        try:
            # 只计算最快的aHash
            if len(image1.shape) == 3:
                image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
            if len(image2.shape) == 3:
                image2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
            
            ahash1 = self._calculate_ahash(image1)
            ahash2 = self._calculate_ahash(image2)
            
            distance = self._hamming_distance(ahash1, ahash2)
            similarity = 1.0 - (distance / (self.hash_size * self.hash_size))
            
            return similarity >= threshold
            
        except Exception as e:
            logger.warning(f"快速相似度判断失败: {str(e)}")
            return False
    
    def batch_compare(self, target_image: np.ndarray, candidate_images: list, threshold: float = 0.8) -> list:
        """
        批量比较目标图像与候选图像列表
        
        Args:
            target_image: 目标图像
            candidate_images: 候选图像列表
            threshold: 相似度阈值
            
        Returns:
            list: 相似图像的索引列表
        """
        similar_indices = []
        
        # 预计算目标图像的哈希值
        if len(target_image.shape) == 3:
            target_image = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)
        target_hash = self._calculate_ahash(target_image)
        
        for i, candidate in enumerate(candidate_images):
            try:
                if len(candidate.shape) == 3:
                    candidate = cv2.cvtColor(candidate, cv2.COLOR_BGR2GRAY)
                
                candidate_hash = self._calculate_ahash(candidate)
                distance = self._hamming_distance(target_hash, candidate_hash)
                similarity = 1.0 - (distance / (self.hash_size * self.hash_size))
                
                if similarity >= threshold:
                    similar_indices.append(i)
                    
            except Exception as e:
                logger.warning(f"批量比较第{i}张图像失败: {str(e)}")
                continue
        
        logger.info(f"批量比较完成: {len(similar_indices)}/{len(candidate_images)} 张图像相似")
        return similar_indices
