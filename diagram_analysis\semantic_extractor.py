"""
图形语义提取模块

使用多模态模型将软件图形转换为Mermaid等结构化格式
"""

import cv2
import numpy as np
import base64
import io
import json
import re
from typing import Dict, Any, List, Optional
from PIL import Image
import requests
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("semantic_extractor", CONFIG)


class SemanticExtractor:
    """图形语义提取器"""
    
    def __init__(self, config: dict = None):
        """
        初始化语义提取器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        
        # 多模态模型配置
        self.vision_config = self.config.get('vision_model', {})
        self.base_url = self.vision_config.get('base_url', '')
        self.api_key = self.vision_config.get('api_key', '')
        self.model_name = self.vision_config.get('model_name', 'Qwen2-VL-7B')
        
        # 输出格式配置
        self.output_formats = self.config.get('semantic_analysis', {}).get('output_formats', ['mermaid', 'json'])
        
        # Mermaid模板
        self.mermaid_templates = self._build_mermaid_templates()
        
        # 提示词模板
        self.extraction_prompts = self._build_extraction_prompts()
    
    def extract_semantics(self, image: np.ndarray, diagram_type: str = 'auto') -> Dict[str, Any]:
        """
        提取图形语义信息
        
        Args:
            image: 输入图像
            diagram_type: 图形类型，'auto'表示自动识别
            
        Returns:
            dict: 语义提取结果
        """
        try:
            logger.info(f"开始语义提取: 图形类型={diagram_type}")
            
            # 如果需要自动识别类型
            if diagram_type == 'auto':
                diagram_type = self._quick_classify(image)
            
            # 生成Mermaid格式
            mermaid_result = self._extract_to_mermaid(image, diagram_type)
            
            # 生成结构化JSON
            json_result = self._extract_to_json(image, diagram_type)
            
            # 生成文本描述
            text_result = self._extract_to_text(image, diagram_type)
            
            # 整合结果
            result = {
                'diagram_type': diagram_type,
                'mermaid': mermaid_result,
                'structured_json': json_result,
                'text_description': text_result,
                'extraction_success': True
            }
            
            logger.info(f"语义提取完成: 类型={diagram_type}")
            return result
            
        except Exception as e:
            logger.error(f"语义提取失败: {str(e)}")
            return {
                'diagram_type': diagram_type,
                'mermaid': {'code': '', 'success': False, 'error': str(e)},
                'structured_json': {'data': {}, 'success': False, 'error': str(e)},
                'text_description': {'description': '', 'success': False, 'error': str(e)},
                'extraction_success': False,
                'error': str(e)
            }
    
    def _extract_to_mermaid(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取为Mermaid格式"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['mermaid'].get(diagram_type, 
                                                          self.extraction_prompts['mermaid']['general'])
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            # 解析Mermaid代码
            mermaid_code = self._extract_mermaid_code(response)
            
            # 验证Mermaid语法
            is_valid = self._validate_mermaid_syntax(mermaid_code, diagram_type)
            
            return {
                'code': mermaid_code,
                'raw_response': response,
                'is_valid': is_valid,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Mermaid提取失败: {str(e)}")
            return {
                'code': '',
                'raw_response': '',
                'is_valid': False,
                'success': False,
                'error': str(e)
            }
    
    def _extract_to_json(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取为结构化JSON"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['json'].get(diagram_type,
                                                        self.extraction_prompts['json']['general'])
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            # 解析JSON数据
            json_data = self._extract_json_data(response)
            
            return {
                'data': json_data,
                'raw_response': response,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"JSON提取失败: {str(e)}")
            return {
                'data': {},
                'raw_response': '',
                'success': False,
                'error': str(e)
            }
    
    def _extract_to_text(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取为文本描述"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['text'].get(diagram_type,
                                                        self.extraction_prompts['text']['general'])
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            return {
                'description': response.strip(),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"文本描述提取失败: {str(e)}")
            return {
                'description': '',
                'success': False,
                'error': str(e)
            }
    
    def _call_vision_model(self, image: np.ndarray, prompt: str) -> str:
        """调用多模态模型"""
        try:
            # 将图像转换为base64
            image_base64 = self._image_to_base64(image)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 4096,
                "temperature": 0.1
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                raise Exception(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"多模态模型调用失败: {str(e)}")
            raise
    
    def _quick_classify(self, image: np.ndarray) -> str:
        """快速分类图形类型"""
        try:
            prompt = """请快速识别这张图的类型，只返回以下类型之一：
- flowchart (流程图)
- sequence (时序图) 
- class (类图)
- usecase (用例图)
- activity (活动图)
- state (状态图)
- component (组件图)
- deployment (部署图)
- other (其他)

只返回类型名称，不要其他内容。"""
            
            response = self._call_vision_model(image, prompt)
            diagram_type = response.strip().lower()
            
            # 验证返回的类型
            valid_types = ['flowchart', 'sequence', 'class', 'usecase', 'activity', 'state', 'component', 'deployment']
            if diagram_type in valid_types:
                return diagram_type
            else:
                return 'flowchart'  # 默认为流程图
                
        except Exception as e:
            logger.warning(f"快速分类失败: {str(e)}")
            return 'flowchart'
    
    def _build_mermaid_templates(self) -> Dict[str, str]:
        """构建Mermaid模板"""
        return {
            'flowchart': """
flowchart TD
    A[开始] --> B[处理步骤]
    B --> C{决策点}
    C -->|是| D[结果1]
    C -->|否| E[结果2]
    D --> F[结束]
    E --> F
""",
            'sequence': """
sequenceDiagram
    participant A as 参与者A
    participant B as 参与者B
    A->>B: 消息1
    B-->>A: 响应1
    A->>B: 消息2
""",
            'class': """
classDiagram
    class ClassName {
        +attribute1: type
        +attribute2: type
        +method1()
        +method2()
    }
"""
        }
    
    def _build_extraction_prompts(self) -> Dict[str, Dict[str, str]]:
        """构建提取提示词"""
        return {
            'mermaid': {
                'flowchart': """请分析这张流程图，将其转换为Mermaid flowchart格式。

要求：
1. 识别所有节点（开始、结束、处理、决策等）
2. 识别所有连接关系和流向
3. 提取节点中的文本内容
4. 使用标准的Mermaid语法

请直接返回Mermaid代码，格式如下：
```mermaid
flowchart TD
    节点定义和连接关系
```""",
                
                'sequence': """请分析这张时序图，将其转换为Mermaid sequence diagram格式。

要求：
1. 识别所有参与者/对象
2. 识别消息传递的顺序和方向
3. 提取消息内容和类型
4. 保持时序关系

请直接返回Mermaid代码，格式如下：
```mermaid
sequenceDiagram
    参与者定义和消息传递
```""",
                
                'class': """请分析这张类图，将其转换为Mermaid class diagram格式。

要求：
1. 识别所有类及其属性和方法
2. 识别类之间的关系（继承、关联等）
3. 保持访问修饰符信息
4. 使用标准UML符号

请直接返回Mermaid代码，格式如下：
```mermaid
classDiagram
    类定义和关系
```""",
                
                'general': """请分析这张软件工程图形，将其转换为合适的Mermaid格式。

要求：
1. 首先判断图形类型（流程图、时序图、类图等）
2. 识别图中的关键元素和关系
3. 转换为对应的Mermaid语法
4. 保持原图的逻辑结构

请直接返回Mermaid代码，以```mermaid开始，```结束。"""
            },
            
            'json': {
                'general': """请分析这张软件工程图形，提取其结构化信息为JSON格式。

要求：
1. 识别图形类型
2. 提取所有节点/元素及其属性
3. 提取所有连接关系
4. 提取文本内容

请返回JSON格式，包含以下结构：
{
    "type": "图形类型",
    "elements": [
        {
            "id": "元素ID",
            "type": "元素类型",
            "text": "文本内容",
            "properties": {}
        }
    ],
    "connections": [
        {
            "from": "起始元素ID",
            "to": "目标元素ID",
            "type": "连接类型",
            "label": "连接标签"
        }
    ]
}"""
            },
            
            'text': {
                'general': """请分析这张软件工程图形，用自然语言描述其内容和逻辑。

要求：
1. 描述图形的整体结构和目的
2. 说明主要的流程或关系
3. 解释关键的业务逻辑
4. 使用清晰易懂的语言

请提供详细的文字描述。"""
            }
        }
    
    def _extract_mermaid_code(self, response: str) -> str:
        """从响应中提取Mermaid代码"""
        # 查找```mermaid代码块
        pattern = r'```mermaid\s*(.*?)\s*```'
        match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(1).strip()
        
        # 如果没有找到代码块，查找其他可能的格式
        pattern = r'```\s*(.*?)\s*```'
        match = re.search(pattern, response, re.DOTALL)
        
        if match:
            code = match.group(1).strip()
            # 检查是否包含mermaid关键词
            if any(keyword in code.lower() for keyword in ['flowchart', 'sequencediagram', 'classdiagram']):
                return code
        
        # 如果都没有找到，返回原始响应
        return response.strip()
    
    def _extract_json_data(self, response: str) -> Dict[str, Any]:
        """从响应中提取JSON数据"""
        try:
            # 查找JSON代码块
            if '{' in response and '}' in response:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {}
        except Exception as e:
            logger.warning(f"JSON解析失败: {str(e)}")
            return {}
    
    def _validate_mermaid_syntax(self, mermaid_code: str, diagram_type: str) -> bool:
        """验证Mermaid语法"""
        try:
            # 基本语法检查
            if not mermaid_code.strip():
                return False
            
            # 检查是否包含对应的图形类型关键词
            type_keywords = {
                'flowchart': ['flowchart'],
                'sequence': ['sequencediagram'],
                'class': ['classdiagram'],
                'usecase': ['flowchart', 'graph'],
                'activity': ['flowchart', 'graph'],
                'state': ['statediagram', 'flowchart'],
                'component': ['flowchart', 'graph'],
                'deployment': ['flowchart', 'graph']
            }
            
            keywords = type_keywords.get(diagram_type, [])
            code_lower = mermaid_code.lower()
            
            if keywords and not any(keyword in code_lower for keyword in keywords):
                return False
            
            # 检查基本的连接语法
            connection_patterns = ['-->', '->', '-->>', '--', '-.->', '-.->']
            has_connections = any(pattern in mermaid_code for pattern in connection_patterns)
            
            return has_connections or 'participant' in code_lower or 'class' in code_lower
            
        except Exception as e:
            logger.warning(f"Mermaid语法验证失败: {str(e)}")
            return False
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """将图像转换为base64字符串"""
        # 转换为RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        
        # 转换为base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
