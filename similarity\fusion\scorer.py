"""
多模态融合评分模块

实现多维度加权融合策略，根据题型动态调整权重
"""

import numpy as np
from typing import Dict, Any, List, Optional
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("fusion_scorer", CONFIG)


class MultiModalScorer:
    """多模态相似度融合评分器"""
    
    def __init__(self, config: dict = None):
        """
        初始化多模态融合评分器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('fusion', {})
        
        # 默认权重配置
        self.default_weights = {
            'pixel': 0.25,      # 像素级相似度
            'hash': 0.15,       # 哈希相似度
            'feature': 0.25,    # 特征匹配相似度
            'ocr': 0.35         # OCR文本相似度
        }
        
        # 题型特定权重配置
        self.subject_weights = self.config.get('subject_weights', {
            'math': {           # 数学题：重视特征匹配和OCR
                'pixel': 0.2,
                'hash': 0.1,
                'feature': 0.35,
                'ocr': 0.35
            },
            'chinese': {        # 语文题：重视OCR文本
                'pixel': 0.15,
                'hash': 0.1,
                'feature': 0.15,
                'ocr': 0.6
            },
            'english': {        # 英语题：重视OCR文本
                'pixel': 0.15,
                'hash': 0.1,
                'feature': 0.15,
                'ocr': 0.6
            },
            'physics': {        # 物理题：平衡各项指标
                'pixel': 0.25,
                'hash': 0.15,
                'feature': 0.3,
                'ocr': 0.3
            },
            'chemistry': {      # 化学题：平衡各项指标
                'pixel': 0.25,
                'hash': 0.15,
                'feature': 0.3,
                'ocr': 0.3
            },
            'biology': {        # 生物题：重视OCR和特征
                'pixel': 0.2,
                'hash': 0.1,
                'feature': 0.25,
                'ocr': 0.45
            }
        })
        
        # 置信度阈值
        self.confidence_thresholds = self.config.get('confidence_thresholds', {
            'pixel': 0.3,
            'hash': 0.5,
            'feature': 0.2,
            'ocr': 0.4
        })
    
    def calculate_fusion_score(self, 
                             pixel_results: Dict[str, Any] = None,
                             hash_results: Dict[str, Any] = None,
                             feature_results: Dict[str, Any] = None,
                             ocr_results: Dict[str, Any] = None,
                             subject: str = 'general',
                             question_type: str = 'general') -> Dict[str, Any]:
        """
        计算多模态融合相似度分数
        
        Args:
            pixel_results: 像素级相似度结果
            hash_results: 哈希相似度结果
            feature_results: 特征匹配相似度结果
            ocr_results: OCR文本相似度结果
            subject: 学科类型
            question_type: 题型
            
        Returns:
            dict: 融合评分结果
        """
        try:
            # 获取权重配置
            weights = self._get_weights(subject, question_type)
            
            # 提取各模态分数
            scores = {}
            confidences = {}
            
            if pixel_results:
                from similarity.pixel_similarity import PixelSimilarityCalculator
                pixel_calc = PixelSimilarityCalculator()
                scores['pixel'] = pixel_calc.get_similarity_score(pixel_results)
                confidences['pixel'] = self._calculate_pixel_confidence(pixel_results)
            
            if hash_results:
                from similarity.hash_similarity import HashSimilarityCalculator
                hash_calc = HashSimilarityCalculator()
                scores['hash'] = hash_calc.get_similarity_score(hash_results)
                confidences['hash'] = self._calculate_hash_confidence(hash_results)
            
            if feature_results:
                from similarity.feature_similarity import FeatureSimilarityCalculator
                feature_calc = FeatureSimilarityCalculator()
                scores['feature'] = feature_calc.get_similarity_score(feature_results)
                confidences['feature'] = self._calculate_feature_confidence(feature_results)
            
            if ocr_results:
                from similarity.ocr_similarity import OCRSimilarityCalculator
                ocr_calc = OCRSimilarityCalculator()
                scores['ocr'] = ocr_calc.get_similarity_score(ocr_results)
                confidences['ocr'] = self._calculate_ocr_confidence(ocr_results)
            
            # 动态调整权重
            adjusted_weights = self._adjust_weights_by_confidence(weights, confidences)
            
            # 计算加权融合分数
            fusion_score = self._calculate_weighted_score(scores, adjusted_weights)
            
            # 计算整体置信度
            overall_confidence = self._calculate_overall_confidence(confidences, adjusted_weights)
            
            # 风险评估
            risk_level = self._assess_risk_level(scores, confidences, fusion_score)
            
            results = {
                'fusion_score': float(fusion_score),
                'overall_confidence': float(overall_confidence),
                'risk_level': risk_level,
                'individual_scores': scores,
                'confidences': confidences,
                'weights': weights,
                'adjusted_weights': adjusted_weights,
                'subject': subject,
                'question_type': question_type,
                'recommendation': self._generate_recommendation(fusion_score, overall_confidence, risk_level)
            }
            
            logger.debug(f"多模态融合完成: 分数={fusion_score:.4f}, 置信度={overall_confidence:.4f}, 风险={risk_level}")
            return results
            
        except Exception as e:
            logger.error(f"多模态融合评分失败: {str(e)}")
            raise
    
    def _get_weights(self, subject: str, question_type: str) -> Dict[str, float]:
        """
        获取权重配置
        
        Args:
            subject: 学科类型
            question_type: 题型
            
        Returns:
            dict: 权重配置
        """
        # 优先使用学科特定权重
        if subject in self.subject_weights:
            weights = self.subject_weights[subject].copy()
        else:
            weights = self.default_weights.copy()
        
        # 根据题型进一步调整权重
        if question_type == 'fill_blank':
            # 填空题：更重视OCR
            weights['ocr'] = min(weights['ocr'] + 0.1, 0.7)
            weights['pixel'] = max(weights['pixel'] - 0.05, 0.1)
            weights['hash'] = max(weights['hash'] - 0.05, 0.05)
        elif question_type == 'calculation':
            # 计算题：更重视特征匹配
            weights['feature'] = min(weights['feature'] + 0.1, 0.5)
            weights['pixel'] = max(weights['pixel'] - 0.05, 0.1)
            weights['hash'] = max(weights['hash'] - 0.05, 0.05)
        elif question_type == 'drawing':
            # 绘图题：更重视像素和特征
            weights['pixel'] = min(weights['pixel'] + 0.1, 0.4)
            weights['feature'] = min(weights['feature'] + 0.1, 0.4)
            weights['ocr'] = max(weights['ocr'] - 0.15, 0.2)
            weights['hash'] = max(weights['hash'] - 0.05, 0.05)
        
        # 确保权重和为1
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight for k, v in weights.items()}
        
        return weights
    
    def _calculate_pixel_confidence(self, results: Dict[str, Any]) -> float:
        """计算像素级相似度的置信度"""
        if not results:
            return 0.0
        
        # 基于SSIM和图像质量评估置信度
        ssim = results.get('ssim', 0.0)
        psnr = results.get('psnr', 0.0)
        
        # SSIM越高，置信度越高
        ssim_confidence = ssim
        
        # PSNR在合理范围内置信度较高
        if psnr == float('inf'):
            psnr_confidence = 1.0
        elif psnr > 30:
            psnr_confidence = 1.0
        elif psnr > 20:
            psnr_confidence = 0.8
        elif psnr > 10:
            psnr_confidence = 0.6
        else:
            psnr_confidence = 0.3
        
        return (ssim_confidence + psnr_confidence) / 2
    
    def _calculate_hash_confidence(self, results: Dict[str, Any]) -> float:
        """计算哈希相似度的置信度"""
        if not results:
            return 0.0
        
        # 基于多种哈希算法的一致性评估置信度
        phash = results.get('phash_similarity', 0.0)
        ahash = results.get('ahash_similarity', 0.0)
        dhash = results.get('dhash_similarity', 0.0)
        
        # 计算方差，方差越小说明一致性越好
        similarities = [phash, ahash, dhash]
        variance = np.var(similarities)
        
        # 方差小于0.1认为一致性好
        consistency_confidence = max(0.0, 1.0 - variance * 10)
        
        # 平均相似度
        avg_similarity = np.mean(similarities)
        
        return (consistency_confidence + avg_similarity) / 2
    
    def _calculate_feature_confidence(self, results: Dict[str, Any]) -> float:
        """计算特征匹配相似度的置信度"""
        if not results:
            return 0.0
        
        # 基于特征点数量和匹配质量评估置信度
        sift_matches = results.get('sift_good_matches', 0)
        orb_matches = results.get('orb_good_matches', 0)
        
        # 特征点匹配数量越多，置信度越高
        match_confidence = min((sift_matches + orb_matches) / 50.0, 1.0)
        
        # 轮廓匹配置信度
        contour_similarity = results.get('contour_similarity', 0.0)
        
        return (match_confidence + contour_similarity) / 2
    
    def _calculate_ocr_confidence(self, results: Dict[str, Any]) -> float:
        """计算OCR文本相似度的置信度"""
        if not results:
            return 0.0
        
        # 基于OCR置信度和文本长度评估
        ocr_confidence = results.get('ocr_confidence', (0.0, 0.0))
        avg_ocr_confidence = np.mean(ocr_confidence)
        
        # 文本长度置信度
        word_count = results.get('word_count', (0, 0))
        total_words = sum(word_count)
        length_confidence = min(total_words / 20.0, 1.0)  # 20个词以上认为充分
        
        return (avg_ocr_confidence + length_confidence) / 2
    
    def _adjust_weights_by_confidence(self, weights: Dict[str, float], confidences: Dict[str, float]) -> Dict[str, float]:
        """
        根据置信度动态调整权重
        
        Args:
            weights: 原始权重
            confidences: 各模态置信度
            
        Returns:
            dict: 调整后的权重
        """
        adjusted_weights = {}
        
        for modality, weight in weights.items():
            confidence = confidences.get(modality, 0.5)
            threshold = self.confidence_thresholds.get(modality, 0.3)
            
            if confidence < threshold:
                # 置信度低，降低权重
                adjusted_weights[modality] = weight * 0.5
            elif confidence > 0.8:
                # 置信度高，增加权重
                adjusted_weights[modality] = weight * 1.2
            else:
                adjusted_weights[modality] = weight
        
        # 重新归一化
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}
        
        return adjusted_weights
    
    def _calculate_weighted_score(self, scores: Dict[str, float], weights: Dict[str, float]) -> float:
        """
        计算加权融合分数
        
        Args:
            scores: 各模态分数
            weights: 权重
            
        Returns:
            float: 融合分数
        """
        weighted_sum = 0.0
        total_weight = 0.0
        
        for modality, weight in weights.items():
            if modality in scores:
                weighted_sum += weight * scores[modality]
                total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return 0.0
    
    def _calculate_overall_confidence(self, confidences: Dict[str, float], weights: Dict[str, float]) -> float:
        """
        计算整体置信度
        
        Args:
            confidences: 各模态置信度
            weights: 权重
            
        Returns:
            float: 整体置信度
        """
        weighted_confidence = 0.0
        total_weight = 0.0
        
        for modality, weight in weights.items():
            if modality in confidences:
                weighted_confidence += weight * confidences[modality]
                total_weight += weight
        
        if total_weight > 0:
            return weighted_confidence / total_weight
        else:
            return 0.0
    
    def _assess_risk_level(self, scores: Dict[str, float], confidences: Dict[str, float], fusion_score: float) -> str:
        """
        评估风险等级
        
        Args:
            scores: 各模态分数
            confidences: 各模态置信度
            fusion_score: 融合分数
            
        Returns:
            str: 风险等级 ('low', 'medium', 'high')
        """
        # 计算分数方差
        score_values = list(scores.values())
        if len(score_values) > 1:
            score_variance = np.var(score_values)
        else:
            score_variance = 0.0
        
        # 计算平均置信度
        avg_confidence = np.mean(list(confidences.values())) if confidences else 0.0
        
        # 风险评估逻辑
        if fusion_score > 0.8 and avg_confidence > 0.7 and score_variance < 0.1:
            return 'low'
        elif fusion_score > 0.6 and avg_confidence > 0.5 and score_variance < 0.2:
            return 'medium'
        else:
            return 'high'
    
    def _generate_recommendation(self, fusion_score: float, confidence: float, risk_level: str) -> str:
        """
        生成建议
        
        Args:
            fusion_score: 融合分数
            confidence: 置信度
            risk_level: 风险等级
            
        Returns:
            str: 建议文本
        """
        if fusion_score > 0.8 and risk_level == 'low':
            return "高度相似，建议进一步人工审核"
        elif fusion_score > 0.6 and risk_level in ['low', 'medium']:
            return "中度相似，建议重点关注"
        elif fusion_score > 0.4:
            return "存在相似性，建议检查"
        else:
            return "相似度较低，可能不存在抄袭"
