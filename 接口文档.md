```python
# 阅卷接口参数（支持单题传入图片解析文字）
class MarkRequest(BaseModel):
    ques_id: str  # 试题ID
    ques_type: str  # 试题类型，填空：D 简答：E
    ques_desc: str  # 试题描述
    std_answer: list  # 试题标准答案 
    stu_answer: list  # 考生作答
    std_score: list  # 答案对应的分数, 简答题是 
    subject: str  # 考试科目
    mark_point: Optional[list] = None  # 得分点 [dict] 
    e_mark_rule: Optional[str] = None  # 简答题评分规则, 传入
    score_rule: Optional[str] = ""  # 全空选择题评分规则
    is_multiple: Optional[int] = 0 # 单空多空标识 ， 0为单空，不乱序按照补全单空逻辑发  1 为多空（可乱序才为1）   
    ques_material: Optional[str] = None  # 组合题试题材料
    same_answer_group_id: Optional[str] = None  # 试题分组ID

# 试题图片文字描述接口(批量)
class ImageRequest(BaseModel):
    ques_data: List[Union[CompositeQuestion, SingleQuestion]]  # 使用明确的类型定义

class CompositeQuestion(BaseModel):
    ques_id: str
    ques_desc: str
    ques_images_dict: Optional[Dict] = None
    ques_children: List["CompositeQuestion"] = []

class SingleQuestion(BaseModel):
    ques_id: str
    ques_desc: str
    ques_images_dict: Optional[Dict] = None
    ques_children: List = []
    '''
    
    ques_data = [{ # 组合题
        ques_id: str, # 试题ID
        ques_desc: str , # 试题描述
        ques_images_dict : Optional[dict] = None
        children:[{
            ques_id: str, # 试题ID
            ques_desc: str , # 试题描述
            ques_images_dict : Optional[dict] = None
            }]

    }],
    [{ # 单题
        ques_id: str, # 试题ID
        ques_desc: str , # 试题描述
        ques_images_dict : Optional[dict] = None
        children:[]
    }]
    '''
    ```

# 阅卷接口返回格式
```python
class Base(BaseModel):
    status: int
    time_cost: str
    data: dict
    """
    {
        ques_id: str,  # 试题ID
        same_answer_group_id: str,  # 试题分组ID
        ai_score: float,
        ai_parse: list,
        ai_score_list: list,
    }
    """
```

request:
```json
{
    "ques_id": "30590004557572729254969346",
    "ques_type": "E",
    "ques_desc": "说出新首都中央商务区建筑的特点及其原因。（8分）",
    "std_answer": [
        "运输距离变长",
        "运输时间增加",
        "短期内可能造成运输成本上涨"
    ],
    "stu_answer": [
        "①高层建筑多，原因：人口密集\n②占地面积大，原因：人口密集\n③基础设施完善，原因：新建，政府规划\n④高科技城市典范，技术新，原因：新建，中埃两国建设者的共同努力\n⑤由中国某建筑公司承建，原因：中国和埃及两国关系友好"
    ],
    "ques_material": "读图文资料，完成下列问题。一千年来，尼罗河畔的开罗是埃及的首都。目前，开罗人满为患，城市拥挤不堪，居住环境不断恶化。2015年，埃及政府规划在开罗以东约45千米的荒漠中建设“新行政首都”（简称新首都，位置如图所示）。2016年，中国某建筑公司承建了新首都重要的中央商务区（CBD）项目。该项目总占地面积约50.5万平方米，包含20个高层建筑单体及配套市政工程（有商务写字楼、公寓楼和大型酒店等），其中最瞩目的是高达385.5米的“非洲第一高楼”标志塔（如图所示）。&nbsp; 经过中埃两国建设者的共同努力，一座被判认为“埃及未来的高科技城市典范”的新首都已拔地而起！",
    "mark_count": 1,
    "std_score": [
        "8.0"
    ],
    "mark_point": [
        {
            "point": "高",
            "score": 2
        },
        {
            "point": "展现国家新面貌",
            "score": 3
        },
        {
            "point": "充分利用土地资源、节约土地",
            "score": 3
        },
        {
            "point": "容纳更多人口",
            "score": 3
        },
        {
            "point": "荒漠中水源紧张",
            "score": 3
        }
    ],
    "subject": "地理",
    "e_mark_rule": "",
    "is_multiple": 1,
    "same_answer_group_id": "20241230224208649003616000"
}```

request E return 
```json

{
    "ques_id": 10001707,
    "ai_score": 3.0,
    "ai_reason": [
        "考生作答与得分点1无关联，给0分",
        "考生作答与得分点2部分相关，”增加政府收入”与经济发展有关，给1分",
        "考生作答与得分点3相关，”不污染环境”和”是可再生资源”与保护环境、低碳环保相关，给2分"
    ],
    "cost_time": 45.79,
    "pending_review": 0,
    "ai_single_score": [
        0.0,
        1.0,
        2.0
    ],
    "same_answer_group_id": "20241230223348382002364500"
}

```



图像批量处理接口返回数据格式：
```python
{
    data:[
    {"ques_id": "10001707",
    "imgs":[
       { "img_id: 'img_desc_text',...
       }
    ]}
],
    "code":200,
    "msg":"success",
    "cost_time": 45.79,
}
```
# 处理图片的性能需要提升  3.19

7.10 
# AI 生成主观题评分标准
# 接口入参
```python
端点名称:supple_mark_point
class MarkPointItem(BaseModel):
    point: str
    socre: float

class Base(BaseModel):
    subject_name: str
    ques_id: str
    ques_type_code: str # D: 填空题 E: 简答题
    ques_desc: str  # 简答题试题描述
    ques_socre: float
    ques_material: str # 组合题试题材料·
    # 业务划分： 
    # 无评分标准，生成评分标准：ques_mark_point为空列表  为试题生成generate_num条评分标准
    # 有评分标准，补充评分标准：ques_mark_point 不为空列表 补充generate_num条评分标准
    ques_mark_point: List[MarkPoint]
    generate_num: int
```

# 出参
```python
class MarkPointItem(BaseModel):
    point: str
    socre: float


class Base(BaseModel):
    code: int
    msg: str
    data: dict
    """
    data 内部键值对
    {
    ques_id: str # 试题id
    generate_mp: list[MarkPointItem] # 生成数量
    ques_mark_rule: str # 试题评分规则
}
```
