"""
LangGraph状态管理模块

定义共享状态类和状态管理机制，支持多Agent协作
"""

import time
from typing import Dict, List, Optional, Any, TypedDict, Annotated
from enum import Enum
import numpy as np
from dataclasses import dataclass, field
from langchain_core.messages import BaseMessage
import operator


class ProcessingStep(Enum):
    """处理步骤枚举"""
    INITIALIZED = "initialized"
    PREPROCESSING = "preprocessing"
    TYPE_CLASSIFICATION = "type_classification"
    SEMANTIC_EXTRACTION = "semantic_extraction"
    RESULT_INTEGRATION = "result_integration"
    COMPLETED = "completed"
    FAILED = "failed"


class AgentType(Enum):
    """Agent类型枚举"""
    SUPERVISOR = "supervisor"
    PREPROCESSOR = "preprocessor"
    CLASSIFIER = "classifier"
    EXTRACTOR = "extractor"
    INTEGRATOR = "integrator"


class AgentReport(TypedDict):
    """Agent执行报告"""
    agent_type: str  # AgentType.value
    step: str  # ProcessingStep.value
    success: bool
    processing_time: float
    message: str
    data: Dict[str, Any]
    error: Optional[str]
    timestamp: float


class ImageQualityReport(TypedDict):
    """图像质量报告"""
    resolution: tuple  # (width, height)
    file_size: int  # 文件大小(bytes)
    format: str  # 图像格式
    color_mode: str  # 颜色模式
    quality_score: float  # 质量评分 0-1
    has_text: bool  # 是否包含文本
    clarity_score: float  # 清晰度评分 0-1
    contrast_score: float  # 对比度评分 0-1
    noise_level: float  # 噪声水平 0-1
    recommendations: List[str]


class TypeClassificationResult(TypedDict):
    """类型分类结果"""
    diagram_type: str
    confidence: float
    alternative_types: List[tuple]  # [(type, confidence), ...]
    reasoning: str
    features_detected: Dict[str, Any]
    validation_passed: bool


class SemanticExtractionResult(TypedDict):
    """语义提取结果"""
    mermaid_code: str
    mermaid_valid: bool
    structured_data: Dict[str, Any]
    text_description: str
    extraction_confidence: float
    elements_count: int
    relationships_count: int
    quality_metrics: Dict[str, float]


class DiagramAnalysisState(TypedDict):
    """图形分析共享状态"""
    
    # === 输入数据 ===
    original_image: str  # base64编码的原始图像
    request_params: Dict[str, Any]  # 请求参数
    session_id: str  # 会话ID
    
    # === 处理状态 ===
    current_step: ProcessingStep  # 当前处理步骤
    processing_history: Annotated[List[str], operator.add]  # 处理历史
    start_time: float  # 开始时间
    
    # === 中间结果 ===
    preprocessed_image: Optional[np.ndarray]  # 预处理后的图像
    image_quality_report: Optional[ImageQualityReport]  # 图像质量报告
    
    classification_result: Optional[TypeClassificationResult]  # 分类结果
    
    extraction_result: Optional[SemanticExtractionResult]  # 提取结果
    
    # === Agent通信 ===
    messages: Annotated[List[BaseMessage], operator.add]  # Agent间消息
    agent_reports: Annotated[List[AgentReport], operator.add]  # Agent报告
    
    # === 最终结果 ===
    final_result: Optional[Dict[str, Any]]  # 最终结果
    
    # === 元数据 ===
    total_processing_time: float  # 总处理时间
    error_messages: Annotated[List[str], operator.add]  # 错误信息
    success: bool  # 处理成功标志
    next_agent: Optional[str]  # 下一个要执行的Agent


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self.state_history: List[DiagramAnalysisState] = []
        self.current_state: Optional[DiagramAnalysisState] = None
    
    def initialize_state(self, 
                        original_image: str, 
                        request_params: Dict[str, Any],
                        session_id: str = None) -> DiagramAnalysisState:
        """初始化状态"""
        if session_id is None:
            session_id = f"session_{int(time.time() * 1000)}"
        
        state = DiagramAnalysisState(
            # 输入数据
            original_image=original_image,
            request_params=request_params,
            session_id=session_id,
            
            # 处理状态
            current_step=ProcessingStep.INITIALIZED,
            processing_history=[],
            start_time=time.time(),
            
            # 中间结果
            preprocessed_image=None,
            image_quality_report=None,
            classification_result=None,
            extraction_result=None,
            
            # Agent通信
            messages=[],
            agent_reports=[],
            
            # 最终结果
            final_result=None,
            
            # 元数据
            total_processing_time=0.0,
            error_messages=[],
            success=False,
            next_agent=None
        )
        
        self.current_state = state
        return state
    
    def update_step(self, state: DiagramAnalysisState, step: ProcessingStep) -> DiagramAnalysisState:
        """更新处理步骤"""
        state["current_step"] = step
        state["processing_history"].append(f"{step.value}:{time.time()}")
        return state
    
    def add_agent_report(self, state: DiagramAnalysisState, report: AgentReport) -> DiagramAnalysisState:
        """添加Agent报告"""
        state["agent_reports"].append(report)
        
        # 如果有错误，添加到错误列表
        if report.get('error'):
            state["error_messages"].append(f"{report['agent_type']}: {report['error']}")
        
        return state
    
    def set_preprocessing_result(self, 
                               state: DiagramAnalysisState,
                               preprocessed_image: np.ndarray,
                               quality_report: ImageQualityReport) -> DiagramAnalysisState:
        """设置预处理结果"""
        state["preprocessed_image"] = preprocessed_image
        state["image_quality_report"] = quality_report
        return state
    
    def set_classification_result(self,
                                state: DiagramAnalysisState,
                                result: TypeClassificationResult) -> DiagramAnalysisState:
        """设置分类结果"""
        state["classification_result"] = result
        return state
    
    def set_extraction_result(self,
                            state: DiagramAnalysisState,
                            result: SemanticExtractionResult) -> DiagramAnalysisState:
        """设置提取结果"""
        state["extraction_result"] = result
        return state
    
    def set_final_result(self,
                        state: DiagramAnalysisState,
                        result: Dict[str, Any]) -> DiagramAnalysisState:
        """设置最终结果"""
        state["final_result"] = result
        state["total_processing_time"] = time.time() - state["start_time"]
        state["success"] = True
        state["current_step"] = ProcessingStep.COMPLETED
        return state
    
    def set_error(self,
                 state: DiagramAnalysisState,
                 error_message: str,
                 agent_type: str = None) -> DiagramAnalysisState:
        """设置错误状态"""
        state["error_messages"].append(error_message)
        state["success"] = False
        state["current_step"] = ProcessingStep.FAILED
        state["total_processing_time"] = time.time() - state["start_time"]
        
        if agent_type:
            error_report: AgentReport = {
                'agent_type': agent_type,
                'step': state["current_step"].value,
                'success': False,
                'processing_time': 0.0,
                'message': "Error occurred",
                'data': {},
                'error': error_message,
                'timestamp': time.time()
            }
            state["agent_reports"].append(error_report)
        
        return state
    
    def get_state_summary(self, state: DiagramAnalysisState) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            "session_id": state["session_id"],
            "current_step": state["current_step"].value,
            "processing_time": time.time() - state["start_time"],
            "success": state["success"],
            "agents_executed": len(state["agent_reports"]),
            "errors_count": len(state["error_messages"]),
            "has_final_result": state["final_result"] is not None
        }
    
    def validate_state(self, state: DiagramAnalysisState) -> tuple[bool, List[str]]:
        """验证状态完整性"""
        errors = []
        
        # 检查必需字段
        if not state.get("original_image"):
            errors.append("Missing original_image")
        
        if not state.get("request_params"):
            errors.append("Missing request_params")
        
        # 检查步骤一致性
        current_step = state.get("current_step")
        if current_step == ProcessingStep.COMPLETED and not state.get("final_result"):
            errors.append("Step is COMPLETED but no final_result")
        
        if current_step == ProcessingStep.FAILED and not state.get("error_messages"):
            errors.append("Step is FAILED but no error_messages")
        
        # 检查数据一致性
        if state.get("classification_result") and not state.get("preprocessed_image"):
            errors.append("Has classification_result but no preprocessed_image")
        
        return len(errors) == 0, errors
    
    def save_state_snapshot(self, state: DiagramAnalysisState) -> str:
        """保存状态快照"""
        snapshot_id = f"snapshot_{state['session_id']}_{int(time.time() * 1000)}"
        self.state_history.append(state.copy())
        return snapshot_id
    
    def get_processing_metrics(self, state: DiagramAnalysisState) -> Dict[str, Any]:
        """获取处理指标"""
        agent_times = {}
        for report in state["agent_reports"]:
            agent_type = report.agent_type.value
            if agent_type not in agent_times:
                agent_times[agent_type] = []
            agent_times[agent_type].append(report.processing_time)
        
        # 计算各Agent平均处理时间
        agent_avg_times = {
            agent: sum(times) / len(times) 
            for agent, times in agent_times.items()
        }
        
        return {
            "total_time": state["total_processing_time"],
            "agent_execution_times": agent_avg_times,
            "steps_completed": len(state["processing_history"]),
            "success_rate": len([r for r in state["agent_reports"] if r.success]) / max(len(state["agent_reports"]), 1),
            "error_count": len(state["error_messages"])
        }


# 全局状态管理器实例
state_manager = StateManager()
