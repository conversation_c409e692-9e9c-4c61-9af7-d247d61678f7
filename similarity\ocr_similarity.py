"""
OCR文本相似度计算模块

集成PaddleOCR进行文字提取，实现TF-IDF、<PERSON>ac<PERSON>、编辑距离、BERT等多种文本相似度算法
这是相似度比对系统的核心模块
"""

import re
import jieba
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
import Levenshtein
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

logger = setup_logger("ocr_similarity", CONFIG)


class OCRSimilarityCalculator:
    """OCR文本相似度计算器"""

    def __init__(self, config: dict = None):
        """
        初始化OCR文本相似度计算器

        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('ocr', {})

        # 初始化PaddleOCR
        self._init_ocr()

        # 初始化BERT模型（可选）
        self._init_bert()

        # 停用词列表
        self.stopwords = self._load_stopwords()

        # TF-IDF向量化器
        self.tfidf_vectorizer = None

    def _init_ocr(self):
        """初始化OCR引擎"""
        if not PADDLEOCR_AVAILABLE:
            self.ocr = None
            logger.warning("PaddleOCR不可用，跳过OCR初始化")
            return

        try:
            ocr_config = self.config.get('paddleocr', {})
            self.ocr = PaddleOCR(
                use_angle_cls=ocr_config.get('use_angle_cls', True),
                lang=ocr_config.get('lang', 'ch'),
                use_gpu=ocr_config.get('use_gpu', False),
                show_log=ocr_config.get('show_log', False)
            )
            logger.info("PaddleOCR初始化成功")
        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {str(e)}")
            self.ocr = None

    def _init_bert(self):
        """初始化BERT模型"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.bert_model = None
            logger.info("sentence-transformers不可用，跳过BERT初始化")
            return

        try:
            model_name = self.config.get(
                'bert_model', 'paraphrase-multilingual-MiniLM-L12-v2')
            self.bert_model = SentenceTransformer(model_name)
            logger.info(f"BERT模型初始化成功: {model_name}")
        except Exception as e:
            logger.warning(f"BERT模型初始化失败: {str(e)}")
            self.bert_model = None

    def _load_stopwords(self) -> set:
        """加载停用词"""
        default_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
        }

        # 可以从配置文件加载更多停用词
        custom_stopwords = set(self.config.get('stopwords', []))
        return default_stopwords.union(custom_stopwords)

    def extract_text_from_image(self, image: np.ndarray) -> Dict[str, Any]:
        """
        从图像中提取文本

        Args:
            image: 输入图像

        Returns:
            dict: 包含提取文本和位置信息的字典
        """
        if self.ocr is None:
            logger.warning("OCR引擎不可用")
            return {
                'text': '',
                'raw_results': [],
                'confidence': 0.0,
                'word_count': 0
            }

        try:
            # 使用PaddleOCR提取文本
            results = self.ocr.ocr(image, cls=True)

            if not results or not results[0]:
                return {
                    'text': '',
                    'raw_results': [],
                    'confidence': 0.0,
                    'word_count': 0
                }

            # 解析OCR结果
            extracted_text = []
            confidences = []
            raw_results = []

            for line in results[0]:
                if line:
                    bbox, (text, confidence) = line
                    extracted_text.append(text)
                    confidences.append(confidence)
                    raw_results.append({
                        'bbox': bbox,
                        'text': text,
                        'confidence': confidence
                    })

            # 合并文本
            full_text = ' '.join(extracted_text)
            avg_confidence = np.mean(confidences) if confidences else 0.0

            logger.debug(
                f"OCR提取完成: 文本长度={len(full_text)}, 平均置信度={avg_confidence:.3f}")

            return {
                'text': full_text,
                'raw_results': raw_results,
                'confidence': float(avg_confidence),
                'word_count': len(extracted_text)
            }

        except Exception as e:
            logger.error(f"OCR文本提取失败: {str(e)}")
            return {
                'text': '',
                'raw_results': [],
                'confidence': 0.0,
                'word_count': 0
            }

    def preprocess_text(self, text: str) -> str:
        """
        预处理文本

        Args:
            text: 原始文本

        Returns:
            str: 预处理后的文本
        """
        if not text:
            return ""

        # 1. 去除特殊字符和标点
        text = re.sub(r'[^\w\s]', ' ', text)

        # 2. 去除多余空格
        text = re.sub(r'\s+', ' ', text).strip()

        # 3. 转换为小写（对中文无效，但对英文有用）
        text = text.lower()

        # 4. 分词
        words = list(jieba.cut(text))

        # 5. 去除停用词
        words = [word for word in words if word not in self.stopwords and len(
            word.strip()) > 0]

        return ' '.join(words)

    def calculate_similarity(self, image1: np.ndarray, image2: np.ndarray, method: str = 'tfidf') -> Dict[str, Any]:
        """
        计算两张图像的OCR文本相似度

        Args:
            image1: 第一张图像
            image2: 第二张图像
            method: 相似度计算方法 ('tfidf', 'jaccard', 'levenshtein', 'bert')

        Returns:
            dict: 包含相似度结果的字典
        """
        try:
            # 提取文本
            ocr_result1 = self.extract_text_from_image(image1)
            ocr_result2 = self.extract_text_from_image(image2)

            text1 = ocr_result1['text']
            text2 = ocr_result2['text']

            if not text1 and not text2:
                # 两张图都没有文本
                return {
                    'similarity': 1.0,
                    'method': method,
                    'text1': text1,
                    'text2': text2,
                    'ocr_confidence': (ocr_result1['confidence'], ocr_result2['confidence']),
                    'word_count': (ocr_result1['word_count'], ocr_result2['word_count'])
                }
            elif not text1 or not text2:
                # 只有一张图有文本
                return {
                    'similarity': 0.0,
                    'method': method,
                    'text1': text1,
                    'text2': text2,
                    'ocr_confidence': (ocr_result1['confidence'], ocr_result2['confidence']),
                    'word_count': (ocr_result1['word_count'], ocr_result2['word_count'])
                }

            # 预处理文本
            processed_text1 = self.preprocess_text(text1)
            processed_text2 = self.preprocess_text(text2)

            # 根据方法计算相似度
            if method == 'tfidf':
                similarity = self._calculate_tfidf_similarity(
                    processed_text1, processed_text2)
            elif method == 'jaccard':
                similarity = self._calculate_jaccard_similarity(
                    processed_text1, processed_text2)
            elif method == 'levenshtein':
                similarity = self._calculate_levenshtein_similarity(
                    processed_text1, processed_text2)
            elif method == 'bert':
                similarity = self._calculate_bert_similarity(
                    text1, text2)  # 使用原始文本
            else:
                raise ValueError(f"不支持的相似度计算方法: {method}")

            return {
                'similarity': float(similarity),
                'method': method,
                'text1': text1,
                'text2': text2,
                'processed_text1': processed_text1,
                'processed_text2': processed_text2,
                'ocr_confidence': (ocr_result1['confidence'], ocr_result2['confidence']),
                'word_count': (ocr_result1['word_count'], ocr_result2['word_count'])
            }

        except Exception as e:
            logger.error(f"OCR相似度计算失败: {str(e)}")
            raise

    def _calculate_tfidf_similarity(self, text1: str, text2: str) -> float:
        """
        计算TF-IDF余弦相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0

        try:
            # 创建TF-IDF向量化器
            vectorizer = TfidfVectorizer(
                max_features=1000,
                ngram_range=(1, 2),
                stop_words=None  # 我们已经预处理过了
            )

            # 计算TF-IDF向量
            tfidf_matrix = vectorizer.fit_transform([text1, text2])

            # 计算余弦相似度
            similarity = cosine_similarity(
                tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]

            return similarity

        except Exception as e:
            logger.warning(f"TF-IDF相似度计算失败: {str(e)}")
            return 0.0

    def _calculate_jaccard_similarity(self, text1: str, text2: str) -> float:
        """
        计算Jaccard相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0

        # 转换为词集合
        words1 = set(text1.split())
        words2 = set(text2.split())

        # 计算交集和并集
        intersection = words1.intersection(words2)
        union = words1.union(words2)

        if len(union) == 0:
            return 0.0

        return len(intersection) / len(union)

    def _calculate_levenshtein_similarity(self, text1: str, text2: str) -> float:
        """
        计算基于编辑距离的相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度分数 (0-1)
        """
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0

        # 计算编辑距离
        distance = Levenshtein.distance(text1, text2)
        max_len = max(len(text1), len(text2))

        if max_len == 0:
            return 1.0

        # 转换为相似度
        similarity = 1.0 - (distance / max_len)
        return max(0.0, similarity)

    def _calculate_bert_similarity(self, text1: str, text2: str) -> float:
        """
        计算BERT语义相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度分数 (0-1)
        """
        if self.bert_model is None:
            logger.warning("BERT模型不可用，返回0相似度")
            return 0.0

        if not text1 or not text2:
            return 0.0

        try:
            # 计算句子嵌入
            embeddings = self.bert_model.encode([text1, text2])

            # 计算余弦相似度
            similarity = cosine_similarity(
                [embeddings[0]], [embeddings[1]])[0][0]

            # 将相似度从[-1,1]映射到[0,1]
            similarity = (similarity + 1) / 2

            return similarity

        except Exception as e:
            logger.warning(f"BERT相似度计算失败: {str(e)}")
            return 0.0

    def calculate_multi_method_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        使用多种方法计算文本相似度

        Args:
            image1: 第一张图像
            image2: 第二张图像

        Returns:
            dict: 包含多种方法相似度结果的字典
        """
        methods = ['tfidf', 'jaccard', 'levenshtein']
        if self.bert_model is not None:
            methods.append('bert')

        results = {}

        for method in methods:
            try:
                result = self.calculate_similarity(image1, image2, method)
                results[f'{method}_similarity'] = result['similarity']

                # 只保存一次文本信息
                if 'text1' not in results:
                    results['text1'] = result['text1']
                    results['text2'] = result['text2']
                    results['ocr_confidence'] = result['ocr_confidence']
                    results['word_count'] = result['word_count']

            except Exception as e:
                logger.warning(f"方法{method}计算失败: {str(e)}")
                results[f'{method}_similarity'] = 0.0

        return results

    def get_similarity_score(self, results: Dict[str, Any]) -> float:
        """
        根据各项文本相似度指标计算综合相似度分数

        Args:
            results: 文本相似度计算结果

        Returns:
            float: 综合相似度分数 (0-1)
        """
        # 权重配置
        weights = self.config.get('weights', {
            'tfidf': 0.4,
            'jaccard': 0.2,
            'levenshtein': 0.2,
            'bert': 0.2
        })

        # 计算加权平均
        score = 0.0
        total_weight = 0.0

        for method, weight in weights.items():
            key = f'{method}_similarity'
            if key in results:
                score += weight * results[key]
                total_weight += weight

        if total_weight > 0:
            score = score / total_weight

        return max(0.0, min(1.0, score))  # 确保分数在[0,1]范围内
