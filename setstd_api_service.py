import time
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from typing import List, Dict, Any
from scripts.request_opera import MarkModel, mark_request_parse
from scripts.ques_opera import model_gen, model_gen_all
from utils.config_manager import CONFIG
from utils.log_utils import setup_logger
import psutil
from scripts.prompt_oprea import create_prompt_from_request
from scripts.request_opera import ImageRequest, handle_image_request  # 新增导入
from functools import partial
from pydantic import ValidationError
import requests
# 在现有导入后添加
from scripts.request_opera import SuppleMarkPointRequest
from scripts.ques_opera import generate_mark_points
# 添加相似度比对相关导入
from similarity.similarity_main import compare_two_answers
from pydantic import BaseModel
# 添加图形语义分析相关导入
from diagram_analysis.langgraph import get_workflow_instance

# 初始化应用
app = FastAPI(title="智能阅卷API服务")
img_logger = setup_logger("api_service", CONFIG)  # 修改为setup_logger并传入配置
logger = setup_logger(name="api_service", config=None)
# 获取API配置
api_config = CONFIG['api_service']

# 初始化线程池
executor = ThreadPoolExecutor(max_workers=api_config['thread_pool_workers'])

# 配置限流器
# if api_config.get('rate_limit', {}).get('enabled', False):
#     limiter = Limiter(key_func=get_remote_address)
#     app.state.limiter = limiter
#     app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境中应该限制具体域名
)


@app.get("/model_health")
def check_model_service():
    try:
        # 检查健康状态 LLM
        llm_health_url = f"{CONFIG['model_service']['api_model']['base_url'].split('/v1')[0]}/health"
        llm_health_resp = requests.get(llm_health_url)
        vlm_health_url = f"{CONFIG['model_service']['vl_model']['api']['base_url'].split('/v1')[0]}/health"
        vlm_health_resp = requests.get(vlm_health_url)
        if llm_health_resp.status_code == 200 and vlm_health_resp.status_code == 200:
            return {"msg": "模型服务运行正常", "state": "success", "code": 200}
        elif llm_health_resp.status_code != 200:
            return {"msg": "LLM模型服务运行异常", "state": "failed", "code": 500}
        elif vlm_health_resp.status_code != 200:
            return {"msg": "VLM模型服务运行异常", "state": "failed", "code": 500}
    except Exception as e:
        logger.error(f"模型服务异常{e}")


# @limiter.limit(f"{api_config['rate_limit']['requests_per_minute']}/minute")
@app.post("/set_std")
async def set_std(request: Request, mark_request: MarkModel):
    """标准设置接口"""
    start_time = time.time()
    # logger.info(mark_request)
    return_data = None
    try:
        # 参数验证
        if not mark_request:
            raise ValueError("请求数据不能为空")

        # 解析请求数据
        request_data = mark_request_parse(mark_request)

        if not request_data.get("subject"):
            raise ValueError("学科信息不能为空")

        # 生成prompt
        first_prompt, total_score = create_prompt_from_request(request_data)

        # 执行模型推理
        loop = asyncio.get_event_loop()
        turn_list = []

        # 设置超时
        timeout = api_config['timeout']['questions']
        results = (-1, [], [])
        try:
            tasks = []
            if request_data.get("ques_type") == "D":
                if request_data.get("is_multiple") == 1:
                    # 多题目评阅使用 model_gen_all
                    task = loop.run_in_executor(
                        executor,
                        model_gen_all,
                        first_prompt,
                        request_data["subject"],
                        len(request_data["stu_answer"]),
                        total_score,
                        request_data.get("ques_type")
                    )
                else:
                    # 单题目评阅使用 model_gen
                    task = loop.run_in_executor(
                        executor,
                        model_gen,
                        first_prompt,
                        request_data["subject"]
                    )
                tasks.append(task)

                results = await asyncio.wait_for(
                    asyncio.gather(*tasks),
                    timeout=timeout
                )
            if request_data.get("ques_type") == "E":
                task = loop.run_in_executor(
                    executor,
                    model_gen_all,
                    first_prompt,
                    request_data["subject"],
                    len(request_data['mark_point']),
                    total_score,
                    request_data.get("ques_type"),
                    request_data.get("mark_point")
                )
                tasks.append(task)

                results = await asyncio.wait_for(
                    asyncio.gather(*tasks),
                    timeout=timeout
                )
        except asyncio.TimeoutError:
            logger.error("模型推理超时")
            raise HTTPException(status_code=408, detail="模型推理超时")
        except Exception as e:
            logger.error(f"模型推理失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"模型推理失败: {str(e)}")

        if not results:
            raise ValueError("评阅任务失败")

        # 计算耗时
        duration = time.time() - start_time

        # 返回结果
        return_data = {
            "data": {"ques_id": mark_request.ques_id,
                     "same_answer_group_id": mark_request.same_answer_group_id,
                     "ai_score": results[0][0],
                     "ai_parse": results[0][1],
                     "ai_score_list": results[0][2]},
            "code": 200,
            "msg": "success",
            "costtime": duration
        }
        return return_data

    except asyncio.TimeoutError:
        logger.error("请求处理超时")
        raise HTTPException(status_code=408, detail="请求处理超时")
    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"return_data:    {return_data}")


@app.post("/batch_supple_images_desc")
async def batch_supple_images_desc(
    request: Request,
    image_request: ImageRequest  # 从request_opera导入ImageRequest模型
):
    """批量补充图片描述接口"""
    start_time = time.time()

    try:
        # 参数验证
        if not image_request.ques_data:
            raise HTTPException(status_code=400, detail="请求数据不能为空")

        # 使用线程池处理IO密集型任务
        loop = asyncio.get_event_loop()
        processed_results = await loop.run_in_executor(
            executor,
            partial(handle_image_request, image_request)
        )

        # 添加耗时统计
        processed_results["cost_time"] = round(time.time() - start_time, 2)

        # 记录成功日志
        logger.info(
            f"批量图片处理完成: 总数={len(image_request.ques_data)} "
            f"耗时={processed_results['cost_time']}s"
        )

        return {
            "msg": "success" if processed_results["code"] == 200 else "partial_success",
            **processed_results
        }

    except ValidationError as ve:
        logger.error(f"请求数据验证失败: {str(ve)}")
        raise HTTPException(status_code=422, detail=str(ve))
    except Exception as e:
        logger.error(f"图片处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"图片处理服务异常: {str(e)}"
        )

# 添加请求日志中间件


@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time

    log_data = {
        "client_ip": request.client.host,
        "method": request.method,
        "path": request.url.path,
        "status_code": response.status_code,
        "duration": f"{duration:.2f}秒"
    }
    # logger.info(f"API请求: {log_data}")

    return response

# 添加健康检查接口


@app.get("/health")
async def health_check():
    """系统健康检查接口"""
    try:
        health_data = {
            "status": "healthy",
            "timestamp": time.time(),
            "system": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            },
            "service": {
                "thread_pool": {
                    "max_workers": executor._max_workers,
                    "active_threads": len(executor._threads)
                }
            }
        }

        # 检查系统资源使用情况
        if (health_data["system"]["cpu_percent"] > 90 or
            health_data["system"]["memory_percent"] > 90 or
                health_data["system"]["disk_usage"] > 90):
            health_data["status"] = "warning"

        return health_data

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }

# 在现有路由后添加


@app.post("/supple_mark_point")
async def supple_mark_point(
    request: Request,
    mark_point_request: SuppleMarkPointRequest
):
    """
    AI生成主观题评分标准接口

    Args:
        mark_point_request: 评分标准生成请求

    Returns:
        生成的评分标准和规则
    """
    start_time = time.time()

    try:
        # 参数验证
        if not mark_point_request.ques_desc:
            raise ValueError("试题描述不能为空")

        if mark_point_request.ques_score <= 0:
            raise ValueError("试题分数必须大于0")

        if mark_point_request.generate_num <= 0:
            raise ValueError("生成数量必须大于0")

        # 检查试题类型
        if mark_point_request.ques_type_code not in ["D", "E"]:
            raise ValueError("试题类型代码必须是D（填空题）或E（简答题）")

        # 转换现有评分点格式
        existing_points = None
        if mark_point_request.ques_mark_point:
            existing_points = [
                {"point": mp.point, "score": mp.score}
                for mp in mark_point_request.ques_mark_point
            ]

        # 使用线程池执行生成任务
        loop = asyncio.get_event_loop()
        task = loop.run_in_executor(
            executor,
            generate_mark_points,
            mark_point_request.subject_name,
            mark_point_request.ques_desc,
            mark_point_request.ques_score,
            mark_point_request.generate_num,
            mark_point_request.ques_material,
            existing_points
        )

        # 设置超时
        timeout = api_config['timeout']['questions']

        try:
            mark_points, mark_rule = await asyncio.wait_for(task, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error("评分标准生成超时")
            raise HTTPException(status_code=408, detail="评分标准生成超时")

        if not mark_points:
            raise ValueError("评分标准生成失败")

        # 计算耗时
        duration = time.time() - start_time

        # 构建返回数据
        return_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "ques_id": mark_point_request.ques_id,
                "generate_mp": mark_points,
                "ques_mark_rule": mark_rule
            },
            "cost_time": round(duration, 2)
        }

        logger.info(
            f"评分标准生成成功: ques_id={mark_point_request.ques_id}, 生成{len(mark_points)}个评分点")
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"评分标准生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"return_data:    {return_data}")


# 相似度比对请求模型
class SimilarityRequest(BaseModel):
    image1: str  # base64编码的图片
    image2: str  # base64编码的图片
    subject: str = "general"  # 学科类型
    question_type: str = "general"  # 题型
    enable_fast_screening: bool = True  # 是否启用快速筛选


# 图形语义分析请求模型
class DiagramAnalysisRequest(BaseModel):
    image: str  # base64编码的图片
    output_format: str = "mermaid"  # 输出格式: mermaid, json, text, all
    diagram_type: str = "auto"  # 图形类型: auto, flowchart, sequence, class, etc.
    enable_preprocessing: bool = True  # 是否启用预处理


@app.post("/api/similarity")
async def compare_similarity(request: SimilarityRequest):
    """
    图片相似度比对接口

    Args:
        request: 相似度比对请求

    Returns:
        dict: 相似度比对结果
    """
    start_time = time.time()
    return_data = {}

    try:
        logger.info(
            f"收到相似度比对请求: 学科={request.subject}, 题型={request.question_type}")

        # 参数验证
        if not request.image1 or not request.image2:
            raise ValueError("图片数据不能为空")

        # 在线程池中执行相似度比对
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            partial(
                compare_two_answers,
                image1=request.image1,
                image2=request.image2,
                subject=request.subject,
                question_type=request.question_type
            )
        )

        # 计算总耗时
        duration = time.time() - start_time

        # 构建返回数据
        return_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "similarity_score": result['similarity_score'],
                "confidence": result['confidence'],
                "risk_level": result['risk_level'],
                "recommendation": result['recommendation'],
                "processing_time": result['processing_time'],
                "subject": result['subject'],
                "question_type": result['question_type'],
                "detailed_results": result.get('detailed_results', {}),
                "metadata": result.get('metadata', {})
            },
            "cost_time": round(duration, 2)
        }

        logger.info(
            f"相似度比对成功: 分数={result['similarity_score']:.4f}, 风险={result['risk_level']}")
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"相似度比对失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"similarity_return_data: {return_data}")


@app.post("/api/diagram_analysis")
async def analyze_diagram(request: DiagramAnalysisRequest):
    """
    图形语义分析接口

    Args:
        request: 图形分析请求

    Returns:
        dict: 图形分析结果
    """
    start_time = time.time()
    return_data = {}

    try:
        logger.info(f"收到图形分析请求: 输出格式={request.output_format}, 图形类型={request.diagram_type}")

        # 参数验证
        if not request.image:
            raise ValueError("图片数据不能为空")

        # 验证输出格式
        valid_formats = ['mermaid', 'json', 'text', 'all']
        if request.output_format not in valid_formats:
            raise ValueError(f"不支持的输出格式: {request.output_format}")

        # 获取LangGraph工作流实例
        workflow = get_workflow_instance()

        # 构建请求参数
        request_params = {
            'output_format': request.output_format,
            'diagram_type': request.diagram_type
        }

        # 在线程池中执行图形分析
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            partial(
                workflow.run_sync,
                original_image=request.image,
                request_params=request_params
            )
        )

        # 计算总耗时
        duration = time.time() - start_time

        # 检查结果是否成功
        if not result.get('success', False):
            error_msg = result.get('error', '图形分析失败')
            logger.error(f"LangGraph工作流执行失败: {error_msg}")
            raise Exception(error_msg)

        # 构建返回数据
        if request.output_format == 'mermaid':
            # Mermaid格式的简化返回
            return_data = {
                "code": 200,
                "msg": "success",
                "data": {
                    "diagram_type": result.get('diagram_type', 'unknown'),
                    "mermaid_code": result.get('mermaid_code', ''),
                    "is_valid": result.get('mermaid_valid', False),
                    "success": result.get('success', False),
                    "quality_score": result.get('quality_metrics', {}).get('overall_quality', 0.0)
                },
                "cost_time": round(duration, 2)
            }
        else:
            # 完整格式的返回
            return_data = {
                "code": 200,
                "msg": "success",
                "data": {
                    "diagram_type": result.get('diagram_type', 'unknown'),
                    "success": result.get('success', False),
                    "processing_time": result.get('metadata', {}).get('processing_time', 0),
                    "mermaid_code": result.get('mermaid_code', ''),
                    "structured_data": result.get('structured_data', {}),
                    "description": result.get('description', ''),
                    "quality_metrics": result.get('quality_metrics', {}),
                    "metadata": result.get('metadata', {})
                },
                "cost_time": round(duration, 2)
            }

        logger.info(f"图形分析成功: 类型={result.get('diagram_type', 'unknown')}, 格式={request.output_format}")
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"图形分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"diagram_analysis_return_data: {return_data}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app=app,
        host=api_config['host'],
        port=api_config['port'],
        workers=api_config['workers']
    )
