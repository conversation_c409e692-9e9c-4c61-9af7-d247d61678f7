"""
LangGraph工作流集成

将所有Agent集成到LangGraph工作流中，实现完整的多Agent协作流程
"""

import time
from typing import Dict, Any, List, Optional, Literal
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state_management import (
    DiagramAnalysisState, ProcessingStep, state_manager
)
from .agents.preprocessing_agent import create_preprocessing_agent
from .agents.classification_agent import create_classification_agent
from .agents.semantic_agent import create_semantic_agent
from .agents.integration_agent import create_integration_agent
from .agents.supervisor_agent import create_supervisor_agent

from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("langgraph_workflow", CONFIG)


class DiagramAnalysisWorkflow:
    """图形分析LangGraph工作流"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化工作流
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        
        # 创建所有Agent
        self.agents = {
            'supervisor': create_supervisor_agent(self.config),
            'preprocessor': create_preprocessing_agent(self.config),
            'classifier': create_classification_agent(self.config),
            'extractor': create_semantic_agent(self.config),
            'integrator': create_integration_agent(self.config)
        }
        
        # 创建工作流图
        self.workflow = self._build_workflow()
        
        # 编译工作流
        self.app = self.workflow.compile(
            checkpointer=MemorySaver(),
            interrupt_before=[]  # 可以在特定节点前中断
        )
        
        logger.info("LangGraph工作流初始化完成")
    
    def _build_workflow(self) -> StateGraph:
        """构建工作流图"""
        # 创建状态图
        workflow = StateGraph(DiagramAnalysisState)
        
        # 添加节点
        workflow.add_node("supervisor", self._supervisor_node)
        workflow.add_node("preprocessor", self._preprocessing_node)
        workflow.add_node("classifier", self._classification_node)
        workflow.add_node("extractor", self._extraction_node)
        workflow.add_node("integrator", self._integration_node)
        
        # 设置入口点
        workflow.set_entry_point("supervisor")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "preprocessor": "preprocessor",
                "classifier": "classifier",
                "extractor": "extractor",
                "integrator": "integrator",
                "end": END
            }
        )
        
        # 添加从各Agent回到Supervisor的边
        workflow.add_edge("preprocessor", "supervisor")
        workflow.add_edge("classifier", "supervisor")
        workflow.add_edge("extractor", "supervisor")
        workflow.add_edge("integrator", "supervisor")
        
        return workflow
    
    def _supervisor_node(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """Supervisor节点"""
        try:
            logger.debug(f"执行Supervisor节点: session_id={state.get('session_id')}")
            
            # 如果是初始状态，进行初始化
            if state.get("current_step") == ProcessingStep.INITIALIZED:
                # 设置第一个要执行的Agent
                state["next_agent"] = "preprocessor"
                return state
            
            # 获取当前要执行的Agent
            current_agent = state.get("next_agent", "preprocessor")

            # 检查当前Agent是否已经执行过
            agent_reports = state.get("agent_reports", [])
            current_agent_executed = any(
                report.get("agent_type") == current_agent and report.get("success", False)
                for report in agent_reports
            )

            # 如果当前Agent已经成功执行，移动到下一个Agent
            if current_agent_executed:
                next_agent = self.agents['supervisor']._get_next_agent(current_agent)
                if next_agent is None:
                    # 所有Agent都执行完成
                    state["next_agent"] = "integrator"
                    if current_agent == "integrator":
                        state["next_agent"] = None
                        state = state_manager.update_step(state, ProcessingStep.COMPLETED)
                else:
                    state["next_agent"] = next_agent
            # 如果当前Agent还没有执行或执行失败，保持不变
            
            return state
            
        except Exception as e:
            logger.error(f"Supervisor节点执行失败: {str(e)}")
            return state_manager.set_error(state, f"Supervisor节点失败: {str(e)}", self.agents['supervisor'].agent_type)
    
    def _preprocessing_node(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """预处理节点"""
        try:
            logger.debug(f"执行预处理节点: session_id={state.get('session_id')}")
            return self.agents['preprocessor'].process(state)
        except Exception as e:
            logger.error(f"预处理节点执行失败: {str(e)}")
            return state_manager.set_error(state, f"预处理节点失败: {str(e)}", self.agents['preprocessor'].agent_type)
    
    def _classification_node(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """分类节点"""
        try:
            logger.debug(f"执行分类节点: session_id={state.get('session_id')}")
            return self.agents['classifier'].process(state)
        except Exception as e:
            logger.error(f"分类节点执行失败: {str(e)}")
            return state_manager.set_error(state, f"分类节点失败: {str(e)}", self.agents['classifier'].agent_type)
    
    def _extraction_node(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """提取节点"""
        try:
            logger.debug(f"执行提取节点: session_id={state.get('session_id')}")
            return self.agents['extractor'].process(state)
        except Exception as e:
            logger.error(f"提取节点执行失败: {str(e)}")
            return state_manager.set_error(state, f"提取节点失败: {str(e)}", self.agents['extractor'].agent_type)
    
    def _integration_node(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """集成节点"""
        try:
            logger.debug(f"执行集成节点: session_id={state.get('session_id')}")
            return self.agents['integrator'].process(state)
        except Exception as e:
            logger.error(f"集成节点执行失败: {str(e)}")
            return state_manager.set_error(state, f"集成节点失败: {str(e)}", self.agents['integrator'].agent_type)
    
    def _supervisor_router(self, state: DiagramAnalysisState) -> Literal["preprocessor", "classifier", "extractor", "integrator", "end"]:
        """Supervisor路由器"""
        try:
            # 检查是否完成或失败
            current_step = state.get("current_step")
            if current_step == ProcessingStep.COMPLETED:
                return "end"
            elif current_step == ProcessingStep.FAILED:
                return "end"
            
            # 获取下一个要执行的Agent
            next_agent = state.get("next_agent")
            
            if next_agent == "preprocessor":
                return "preprocessor"
            elif next_agent == "classifier":
                return "classifier"
            elif next_agent == "extractor":
                return "extractor"
            elif next_agent == "integrator":
                return "integrator"
            else:
                # 没有下一个Agent，结束工作流
                return "end"
                
        except Exception as e:
            logger.error(f"Supervisor路由失败: {str(e)}")
            return "end"
    
    async def run_async(self, 
                       original_image: str, 
                       request_params: Dict[str, Any],
                       session_id: str = None) -> Dict[str, Any]:
        """异步运行工作流"""
        try:
            # 初始化状态
            initial_state = state_manager.initialize_state(
                original_image, request_params, session_id
            )
            
            logger.info(f"开始异步执行工作流: session_id={initial_state['session_id']}")
            
            # 运行工作流
            config = {"configurable": {"thread_id": initial_state['session_id']}}
            
            final_state = None
            async for state in self.app.astream(initial_state, config):
                final_state = state
                logger.debug(f"工作流状态更新: step={state.get('current_step')}")
            
            if final_state is None:
                raise Exception("工作流执行失败，未获得最终状态")
            
            # 获取最终结果
            final_result = final_state.get('final_result')
            if final_result is None:
                raise Exception("工作流完成但未生成最终结果")
            
            logger.info(f"异步工作流执行完成: session_id={initial_state['session_id']}")
            return final_result
            
        except Exception as e:
            logger.error(f"异步工作流执行失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id or 'unknown',
                'timestamp': time.time()
            }
    
    def run_sync(self, 
                original_image: str, 
                request_params: Dict[str, Any],
                session_id: str = None) -> Dict[str, Any]:
        """同步运行工作流"""
        try:
            # 初始化状态
            initial_state = state_manager.initialize_state(
                original_image, request_params, session_id
            )
            
            logger.info(f"开始同步执行工作流: session_id={initial_state['session_id']}")
            
            # 运行工作流
            config = {"configurable": {"thread_id": initial_state['session_id']}}
            
            final_state = None
            for state in self.app.stream(initial_state, config):
                final_state = state
                logger.debug(f"工作流状态更新: step={state.get('current_step')}")
            
            if final_state is None:
                raise Exception("工作流执行失败，未获得最终状态")
            
            # 获取最终结果
            final_result = final_state.get('final_result')
            if final_result is None:
                raise Exception("工作流完成但未生成最终结果")
            
            logger.info(f"同步工作流执行完成: session_id={initial_state['session_id']}")
            return final_result
            
        except Exception as e:
            logger.error(f"同步工作流执行失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id or 'unknown',
                'timestamp': time.time()
            }
    
    def get_workflow_status(self, session_id: str) -> Dict[str, Any]:
        """获取工作流状态"""
        try:
            # 这里可以从checkpointer获取状态
            # 暂时返回基本信息
            return {
                'session_id': session_id,
                'status': 'unknown',
                'message': '状态查询功能待实现'
            }
        except Exception as e:
            logger.error(f"获取工作流状态失败: {str(e)}")
            return {
                'session_id': session_id,
                'status': 'error',
                'error': str(e)
            }
    
    def visualize_workflow(self) -> str:
        """可视化工作流图"""
        try:
            # 生成Mermaid图
            mermaid_code = """
graph TD
    A[开始] --> B[Supervisor]
    B --> C{决策}
    C -->|预处理| D[Preprocessor]
    C -->|分类| E[Classifier]
    C -->|提取| F[Extractor]
    C -->|集成| G[Integrator]
    C -->|完成| H[结束]
    D --> B
    E --> B
    F --> B
    G --> B
"""
            return mermaid_code
        except Exception as e:
            logger.error(f"工作流可视化失败: {str(e)}")
            return "工作流可视化失败"


# 全局工作流实例
_workflow_instance = None

def get_workflow_instance(config: Dict[str, Any] = None) -> DiagramAnalysisWorkflow:
    """获取工作流实例（单例模式）"""
    global _workflow_instance
    if _workflow_instance is None:
        _workflow_instance = DiagramAnalysisWorkflow(config)
    return _workflow_instance

def create_workflow(config: Dict[str, Any] = None) -> DiagramAnalysisWorkflow:
    """创建新的工作流实例"""
    return DiagramAnalysisWorkflow(config)
