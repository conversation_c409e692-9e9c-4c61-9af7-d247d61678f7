"""
图形类型识别Agent

使用多模态模型识别软件图形类型的Agent
"""

import time
import base64
import io
import cv2
import numpy as np
import json
import requests
from PIL import Image
from typing import Dict, Any, List, Optional, Tuple
from langchain_core.messages import HumanMessage, AIMessage

from ..state_management import (
    DiagramAnalysisState, AgentReport, AgentType, ProcessingStep,
    TypeClassificationResult, state_manager
)
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("classification_agent", CONFIG)


class DiagramClassificationAgent:
    """图形类型识别Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化分类Agent
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        self.vision_config = self.config.get('vision_model', {})
        self.agent_type = AgentType.CLASSIFIER
        
        # 多模态模型配置
        self.base_url = self.vision_config.get('base_url', '')
        self.api_key = self.vision_config.get('api_key', '')
        self.model_name = self.vision_config.get('model_name', 'Qwen2-VL-7B')
        
        # 支持的图形类型
        self.supported_types = self.config.get('supported_types', [
            'flowchart', 'sequence_diagram', 'class_diagram', 'use_case_diagram',
            'activity_diagram', 'state_diagram', 'component_diagram', 'deployment_diagram'
        ])
        
        # 分类提示词
        self.classification_prompt = self._build_classification_prompt()
        
        # 特征检测规则
        self.feature_rules = self._build_feature_rules()
    
    def process(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """
        执行图形类型识别
        
        Args:
            state: 当前状态
            
        Returns:
            DiagramAnalysisState: 更新后的状态
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始图形类型识别: session_id={state['session_id']}")
            
            # 更新处理步骤
            state = state_manager.update_step(state, ProcessingStep.TYPE_CLASSIFICATION)
            
            # 获取预处理后的图像
            preprocessed_image = state.get('preprocessed_image')
            if preprocessed_image is None:
                raise ValueError("未找到预处理后的图像")
            
            # 检查用户指定的类型
            request_params = state.get('request_params', {})
            specified_type = request_params.get('diagram_type', 'auto')
            
            if specified_type != 'auto' and specified_type in self.supported_types:
                # 用户指定了类型，直接使用
                classification_result = TypeClassificationResult(
                    diagram_type=specified_type,
                    confidence=1.0,
                    alternative_types=[],
                    reasoning="用户指定类型",
                    features_detected={},
                    validation_passed=True
                )
            else:
                # 自动识别类型
                classification_result = self._classify_diagram(preprocessed_image)
            
            # 更新状态
            state = state_manager.set_classification_result(state, classification_result)
            
            # 生成Agent报告
            processing_time = time.time() - start_time
            report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.TYPE_CLASSIFICATION,
                success=True,
                processing_time=processing_time,
                message=f"图形类型识别完成: {classification_result.diagram_type} (置信度: {classification_result.confidence:.3f})",
                data={
                    "diagram_type": classification_result.diagram_type,
                    "confidence": classification_result.confidence,
                    "alternative_types": classification_result.alternative_types,
                    "validation_passed": classification_result.validation_passed
                }
            )
            
            state = state_manager.add_agent_report(state, report)
            
            # 添加消息到状态
            message = AIMessage(
                content=f"图形类型识别完成。识别类型: {classification_result.diagram_type}, "
                       f"置信度: {classification_result.confidence:.3f}, "
                       f"推理: {classification_result.reasoning}"
            )
            state["messages"].append(message)
            
            # 设置下一个Agent
            state["next_agent"] = "extractor"
            
            logger.info(f"图形类型识别成功: 类型={classification_result.diagram_type}, 耗时={processing_time:.2f}s")
            return state
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"图形类型识别失败: {str(e)}"
            logger.error(error_msg)
            
            # 记录错误报告
            error_report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.TYPE_CLASSIFICATION,
                success=False,
                processing_time=processing_time,
                message="图形类型识别失败",
                error=str(e)
            )
            
            state = state_manager.add_agent_report(state, error_report)
            state = state_manager.set_error(state, error_msg, self.agent_type)
            
            return state
    
    def _classify_diagram(self, image: np.ndarray) -> TypeClassificationResult:
        """执行图形分类"""
        try:
            # 多模态模型分类
            vision_result = self._classify_with_vision_model(image)
            
            # 特征验证
            feature_result = self._validate_with_features(image)
            
            # 融合结果
            final_result = self._fuse_classification_results(vision_result, feature_result)
            
            return final_result
            
        except Exception as e:
            logger.error(f"图形分类失败: {str(e)}")
            return TypeClassificationResult(
                diagram_type='flowchart',  # 默认类型
                confidence=0.1,
                alternative_types=[],
                reasoning=f"分类失败，使用默认类型: {str(e)}",
                features_detected={},
                validation_passed=False
            )
    
    def _classify_with_vision_model(self, image: np.ndarray) -> Dict[str, Any]:
        """使用多模态模型进行分类"""
        try:
            # 转换图像为base64
            image_base64 = self._image_to_base64(image)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": self.classification_prompt
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 1024,
                "temperature": 0.1
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                return self._parse_vision_result(content)
            else:
                logger.error(f"多模态模型请求失败: {response.status_code}")
                return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': '模型请求失败'}
                
        except Exception as e:
            logger.error(f"多模态模型分类失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': f'模型调用异常: {str(e)}'}
    
    def _validate_with_features(self, image: np.ndarray) -> Dict[str, Any]:
        """基于特征进行验证"""
        try:
            # 简单的特征检测
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            
            # 检测边缘
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.sum(edges > 0)
            
            # 检测直线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=30, maxLineGap=10)
            line_count = len(lines) if lines is not None else 0
            
            # 检测轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            contour_count = len(contours)
            
            # 基于特征的简单分类
            features = {
                'edge_density': edge_count / (image.shape[0] * image.shape[1]),
                'line_count': line_count,
                'contour_count': contour_count
            }
            
            # 简单的规则分类
            if line_count > 10 and contour_count > 5:
                diagram_type = 'flowchart'
                confidence = 0.6
            elif line_count > 20:
                diagram_type = 'sequence_diagram'
                confidence = 0.5
            else:
                diagram_type = 'unknown'
                confidence = 0.3
            
            return {
                'diagram_type': diagram_type,
                'confidence': confidence,
                'features': features
            }
            
        except Exception as e:
            logger.error(f"特征验证失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'features': {}}
    
    def _fuse_classification_results(self, vision_result: Dict[str, Any], feature_result: Dict[str, Any]) -> TypeClassificationResult:
        """融合分类结果"""
        vision_type = vision_result.get('diagram_type', 'unknown')
        vision_conf = vision_result.get('confidence', 0.0)
        vision_reasoning = vision_result.get('reasoning', '')
        
        feature_type = feature_result.get('diagram_type', 'unknown')
        feature_conf = feature_result.get('confidence', 0.0)
        
        # 权重配置
        vision_weight = 0.8
        feature_weight = 0.2
        
        # 融合逻辑
        if vision_type == feature_type and vision_type != 'unknown':
            # 两种方法结果一致
            final_type = vision_type
            final_conf = min(1.0, vision_conf * vision_weight + feature_conf * feature_weight + 0.1)
            validation_passed = True
        elif vision_conf * vision_weight >= feature_conf * feature_weight:
            # 视觉模型置信度更高
            final_type = vision_type
            final_conf = vision_conf * vision_weight
            validation_passed = vision_conf > 0.5
        else:
            # 特征验证置信度更高
            final_type = feature_type
            final_conf = feature_conf * feature_weight
            validation_passed = False
        
        # 确保类型在支持列表中
        if final_type not in self.supported_types:
            final_type = 'flowchart'  # 默认类型
            final_conf = 0.3
            validation_passed = False
        
        return TypeClassificationResult(
            diagram_type=final_type,
            confidence=final_conf,
            alternative_types=[(feature_type, feature_conf)] if feature_type != final_type else [],
            reasoning=vision_reasoning or "基于多模态模型和特征融合的分类结果",
            features_detected=feature_result.get('features', {}),
            validation_passed=validation_passed
        )
    
    def _build_classification_prompt(self) -> str:
        """构建分类提示词"""
        types_desc = {
            'flowchart': '流程图 - 显示业务流程，包含开始/结束节点、处理节点、决策菱形、连接箭头',
            'sequence_diagram': '时序图 - 显示对象间的交互时序，包含生命线、激活框、消息箭头',
            'class_diagram': '类图 - 显示类的结构和关系，包含类框、属性、方法、继承/关联线',
            'use_case_diagram': '用例图 - 显示系统功能和用户交互，包含用例椭圆、角色、系统边界',
            'activity_diagram': '活动图 - 显示活动流程，包含活动节点、决策点、并发分支',
            'state_diagram': '状态图 - 显示状态转换，包含状态节点、转换箭头、触发条件',
            'component_diagram': '组件图 - 显示组件结构，包含组件框、接口、依赖关系',
            'deployment_diagram': '部署图 - 显示系统部署，包含节点、组件、通信路径'
        }
        
        prompt = """请分析这张软件工程图形，识别其类型。

支持的图形类型：
"""
        for type_key, desc in types_desc.items():
            prompt += f"- {desc}\n"
        
        prompt += """
请按以下JSON格式返回结果：
{
    "diagram_type": "图形类型（使用英文标识符）",
    "confidence": 置信度（0-1之间的数值）,
    "reasoning": "识别理由（简要说明识别依据）"
}

注意：
1. 仔细观察图形中的元素类型、布局特征、连接关系
2. 置信度应该反映识别的确定程度
3. 如果无法确定类型，返回"flowchart"作为默认值
"""
        return prompt
    
    def _build_feature_rules(self) -> Dict[str, Dict[str, Any]]:
        """构建特征匹配规则"""
        return {
            'flowchart': {
                'min_contours': 3,
                'min_lines': 5,
                'edge_density_range': (0.01, 0.1)
            },
            'sequence_diagram': {
                'min_contours': 2,
                'min_lines': 10,
                'edge_density_range': (0.005, 0.05)
            }
        }
    
    def _parse_vision_result(self, content: str) -> Dict[str, Any]:
        """解析多模态模型结果"""
        try:
            # 尝试解析JSON
            if '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                json_str = content[json_start:json_end]
                result = json.loads(json_str)
                
                return {
                    'diagram_type': result.get('diagram_type', 'unknown'),
                    'confidence': float(result.get('confidence', 0.0)),
                    'reasoning': result.get('reasoning', '')
                }
            else:
                # 简单的关键词匹配
                return self._extract_from_text(content)
                
        except Exception as e:
            logger.warning(f"解析多模态模型结果失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': content}
    
    def _extract_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取分类信息"""
        text_lower = text.lower()
        
        # 关键词匹配
        type_keywords = {
            'flowchart': ['flow', 'process', '流程', '业务'],
            'sequence_diagram': ['sequence', 'time', '时序', '交互'],
            'class_diagram': ['class', 'uml', '类图', '类'],
            'use_case_diagram': ['use case', 'actor', '用例', '角色']
        }
        
        best_type = 'flowchart'
        best_score = 0
        
        for diagram_type, keywords in type_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > best_score:
                best_score = score
                best_type = diagram_type
        
        confidence = min(0.7, best_score * 0.2) if best_score > 0 else 0.3
        
        return {
            'diagram_type': best_type,
            'confidence': confidence,
            'reasoning': f'基于关键词匹配: {best_score}个匹配'
        }
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """将图像转换为base64字符串"""
        # 转换为RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        
        # 转换为base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64


def create_classification_agent(config: Dict[str, Any] = None) -> DiagramClassificationAgent:
    """创建图形类型识别Agent"""
    return DiagramClassificationAgent(config)
