"""
像素级相似度计算模块

实现SSIM和MSE算法计算整体布局相似性
"""

import cv2
import numpy as np
from skimage.metrics import structural_similarity as ssim
from typing import Tuple, Dict, Any
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("pixel_similarity", CONFIG)


class PixelSimilarityCalculator:
    """像素级相似度计算器"""
    
    def __init__(self, config: dict = None):
        """
        初始化像素级相似度计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('pixel', {})
        self.ssim_window_size = self.config.get('ssim_window_size', 7)
        self.ssim_k1 = self.config.get('ssim_k1', 0.01)
        self.ssim_k2 = self.config.get('ssim_k2', 0.03)
        
    def calculate_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算两张图像的像素级相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: 包含各种相似度指标的字典
        """
        try:
            # 确保图像尺寸一致
            image1_resized, image2_resized = self._resize_to_same_size(image1, image2)
            
            # 确保图像为灰度图
            if len(image1_resized.shape) == 3:
                image1_resized = cv2.cvtColor(image1_resized, cv2.COLOR_BGR2GRAY)
            if len(image2_resized.shape) == 3:
                image2_resized = cv2.cvtColor(image2_resized, cv2.COLOR_BGR2GRAY)
            
            # 计算SSIM
            ssim_score, ssim_map = self._calculate_ssim(image1_resized, image2_resized)
            
            # 计算MSE
            mse_score = self._calculate_mse(image1_resized, image2_resized)
            
            # 计算PSNR
            psnr_score = self._calculate_psnr(image1_resized, image2_resized, mse_score)
            
            # 计算直方图相似度
            hist_similarity = self._calculate_histogram_similarity(image1_resized, image2_resized)
            
            # 计算梯度相似度
            gradient_similarity = self._calculate_gradient_similarity(image1_resized, image2_resized)
            
            results = {
                'ssim': float(ssim_score),
                'mse': float(mse_score),
                'psnr': float(psnr_score),
                'histogram_similarity': float(hist_similarity),
                'gradient_similarity': float(gradient_similarity),
                'ssim_map': ssim_map,
                'image_shapes': {
                    'original': (image1.shape, image2.shape),
                    'processed': (image1_resized.shape, image2_resized.shape)
                }
            }
            
            logger.debug(f"像素级相似度计算完成: SSIM={ssim_score:.4f}, MSE={mse_score:.4f}")
            return results
            
        except Exception as e:
            logger.error(f"像素级相似度计算失败: {str(e)}")
            raise
    
    def _resize_to_same_size(self, image1: np.ndarray, image2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        将两张图像调整到相同尺寸
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            tuple: 调整尺寸后的两张图像
        """
        h1, w1 = image1.shape[:2]
        h2, w2 = image2.shape[:2]
        
        # 选择较小的尺寸作为目标尺寸
        target_height = min(h1, h2)
        target_width = min(w1, w2)
        
        # 调整图像尺寸
        image1_resized = cv2.resize(image1, (target_width, target_height), interpolation=cv2.INTER_AREA)
        image2_resized = cv2.resize(image2, (target_width, target_height), interpolation=cv2.INTER_AREA)
        
        logger.debug(f"图像尺寸调整: ({w1}x{h1}, {w2}x{h2}) -> {target_width}x{target_height}")
        return image1_resized, image2_resized
    
    def _calculate_ssim(self, image1: np.ndarray, image2: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        计算结构相似性指数(SSIM)
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            tuple: (SSIM分数, SSIM映射图)
        """
        try:
            # 确保窗口大小不超过图像尺寸
            min_dim = min(image1.shape[0], image1.shape[1])
            window_size = min(self.ssim_window_size, min_dim)
            if window_size % 2 == 0:
                window_size -= 1  # 确保窗口大小为奇数
            
            ssim_score, ssim_map = ssim(
                image1, image2,
                win_size=window_size,
                full=True,
                data_range=255,
                K1=self.ssim_k1,
                K2=self.ssim_k2
            )
            
            return ssim_score, ssim_map
            
        except Exception as e:
            logger.warning(f"SSIM计算失败，使用简化方法: {str(e)}")
            # 使用简化的SSIM计算
            return self._simple_ssim(image1, image2)
    
    def _simple_ssim(self, image1: np.ndarray, image2: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        简化的SSIM计算
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            tuple: (SSIM分数, 差异图)
        """
        # 计算均值
        mu1 = np.mean(image1)
        mu2 = np.mean(image2)
        
        # 计算方差和协方差
        var1 = np.var(image1)
        var2 = np.var(image2)
        cov = np.mean((image1 - mu1) * (image2 - mu2))
        
        # SSIM公式
        c1 = (self.ssim_k1 * 255) ** 2
        c2 = (self.ssim_k2 * 255) ** 2
        
        ssim_score = ((2 * mu1 * mu2 + c1) * (2 * cov + c2)) / \
                     ((mu1**2 + mu2**2 + c1) * (var1 + var2 + c2))
        
        # 生成差异图
        diff_map = np.abs(image1.astype(float) - image2.astype(float))
        
        return float(ssim_score), diff_map
    
    def _calculate_mse(self, image1: np.ndarray, image2: np.ndarray) -> float:
        """
        计算均方误差(MSE)
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            float: MSE值
        """
        mse = np.mean((image1.astype(float) - image2.astype(float)) ** 2)
        return mse
    
    def _calculate_psnr(self, image1: np.ndarray, image2: np.ndarray, mse: float = None) -> float:
        """
        计算峰值信噪比(PSNR)
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            mse: 预计算的MSE值
            
        Returns:
            float: PSNR值
        """
        if mse is None:
            mse = self._calculate_mse(image1, image2)
        
        if mse == 0:
            return float('inf')  # 图像完全相同
        
        max_pixel_value = 255.0
        psnr = 20 * np.log10(max_pixel_value / np.sqrt(mse))
        return psnr
    
    def _calculate_histogram_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
        """
        计算直方图相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            float: 直方图相似度
        """
        # 计算直方图
        hist1 = cv2.calcHist([image1], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([image2], [0], None, [256], [0, 256])
        
        # 归一化直方图
        hist1 = hist1 / np.sum(hist1)
        hist2 = hist2 / np.sum(hist2)
        
        # 计算相关系数
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        
        return correlation
    
    def _calculate_gradient_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
        """
        计算梯度相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            float: 梯度相似度
        """
        # 计算梯度
        grad1_x = cv2.Sobel(image1, cv2.CV_64F, 1, 0, ksize=3)
        grad1_y = cv2.Sobel(image1, cv2.CV_64F, 0, 1, ksize=3)
        grad1_magnitude = np.sqrt(grad1_x**2 + grad1_y**2)
        
        grad2_x = cv2.Sobel(image2, cv2.CV_64F, 1, 0, ksize=3)
        grad2_y = cv2.Sobel(image2, cv2.CV_64F, 0, 1, ksize=3)
        grad2_magnitude = np.sqrt(grad2_x**2 + grad2_y**2)
        
        # 计算梯度相似度
        grad_diff = np.abs(grad1_magnitude - grad2_magnitude)
        max_grad = np.maximum(grad1_magnitude, grad2_magnitude)
        
        # 避免除零
        similarity_map = np.where(max_grad > 0, 1 - grad_diff / max_grad, 1.0)
        gradient_similarity = np.mean(similarity_map)
        
        return gradient_similarity
    
    def get_similarity_score(self, results: Dict[str, Any]) -> float:
        """
        根据各项指标计算综合相似度分数
        
        Args:
            results: 相似度计算结果
            
        Returns:
            float: 综合相似度分数 (0-1)
        """
        # 权重配置
        weights = self.config.get('weights', {
            'ssim': 0.4,
            'histogram': 0.3,
            'gradient': 0.2,
            'psnr': 0.1
        })
        
        # 归一化PSNR (通常在20-40范围内)
        psnr_normalized = min(results['psnr'] / 40.0, 1.0) if results['psnr'] != float('inf') else 1.0
        
        # 计算加权平均
        score = (
            weights['ssim'] * results['ssim'] +
            weights['histogram'] * results['histogram_similarity'] +
            weights['gradient'] * results['gradient_similarity'] +
            weights['psnr'] * psnr_normalized
        )
        
        return max(0.0, min(1.0, score))  # 确保分数在[0,1]范围内
