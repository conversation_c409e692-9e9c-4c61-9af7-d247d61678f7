"""
图像预处理Agent

专门负责图像预处理任务的Agent
"""

import time
import base64
import io
import cv2
import numpy as np
from PIL import Image
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage

from ..state_management import (
    DiagramAnalysisState, AgentReport, AgentType, ProcessingStep,
    ImageQualityReport, state_manager
)
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("preprocessing_agent", CONFIG)


class ImagePreprocessingAgent:
    """图像预处理Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化预处理Agent
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        self.processing_config = self.config.get('processing', {})
        self.agent_type = AgentType.PREPROCESSOR
        
        # 预处理参数
        self.max_image_size = self.processing_config.get('max_image_size', [2048, 2048])
        self.quality_threshold = self.processing_config.get('quality_threshold', 0.5)
        self.enable_enhancement = self.processing_config.get('enable_enhancement', True)
        
    def process(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """
        执行图像预处理
        
        Args:
            state: 当前状态
            
        Returns:
            DiagramAnalysisState: 更新后的状态
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始图像预处理: session_id={state['session_id']}")
            
            # 更新处理步骤
            state = state_manager.update_step(state, ProcessingStep.PREPROCESSING)
            
            # 解码图像
            original_image = self._decode_image(state['original_image'])
            if original_image is None:
                raise ValueError("图像解码失败")
            
            # 图像质量评估
            quality_report = self._assess_image_quality(original_image)
            
            # 执行预处理
            preprocessed_image = self._preprocess_image(original_image, quality_report)
            
            # 更新状态
            state = state_manager.set_preprocessing_result(
                state, preprocessed_image, quality_report
            )
            
            # 生成Agent报告
            processing_time = time.time() - start_time
            report: AgentReport = {
                'agent_type': self.agent_type.value,
                'step': ProcessingStep.PREPROCESSING.value,
                'success': True,
                'processing_time': processing_time,
                'message': f"图像预处理完成，质量评分: {quality_report['quality_score']:.3f}",
                'data': {
                    "original_size": list(original_image.shape),
                    "processed_size": list(preprocessed_image.shape),
                    "quality_score": quality_report['quality_score'],
                    "enhancements_applied": quality_report['recommendations']
                },
                'error': None,
                'timestamp': time.time()
            }
            
            state = state_manager.add_agent_report(state, report)
            
            # 添加消息到状态
            message = AIMessage(
                content=f"图像预处理完成。原始尺寸: {original_image.shape}, "
                       f"处理后尺寸: {preprocessed_image.shape}, "
                       f"质量评分: {quality_report['quality_score']:.3f}"
            )
            state["messages"].append(message)
            
            # 设置下一个Agent
            state["next_agent"] = "classifier"
            
            logger.info(f"图像预处理成功: 耗时={processing_time:.2f}s")
            return state
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"图像预处理失败: {str(e)}"
            logger.error(error_msg)
            
            # 记录错误报告
            error_report: AgentReport = {
                'agent_type': self.agent_type.value,
                'step': ProcessingStep.PREPROCESSING.value,
                'success': False,
                'processing_time': processing_time,
                'message': "图像预处理失败",
                'data': {},
                'error': str(e),
                'timestamp': time.time()
            }

            state = state_manager.add_agent_report(state, error_report)
            state = state_manager.set_error(state, error_msg, self.agent_type.value)
            
            return state
    
    def _decode_image(self, image_base64: str) -> Optional[np.ndarray]:
        """解码base64图像"""
        try:
            # 处理data URL格式
            if image_base64.startswith('data:image'):
                image_base64 = image_base64.split(',')[1]
            
            # 解码base64
            image_data = base64.b64decode(image_base64)
            
            # 转换为PIL图像
            pil_image = Image.open(io.BytesIO(image_data))
            
            # 转换为OpenCV格式
            if pil_image.mode == 'RGBA':
                pil_image = pil_image.convert('RGB')
            
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return cv_image
            
        except Exception as e:
            logger.error(f"图像解码失败: {str(e)}")
            return None
    
    def _assess_image_quality(self, image: np.ndarray) -> Dict[str, Any]:
        """评估图像质量"""
        try:
            h, w = image.shape[:2]
            
            # 基本信息
            resolution = (w, h)
            file_size = image.nbytes
            format_type = "BGR"
            color_mode = "Color" if len(image.shape) == 3 else "Grayscale"
            
            # 转换为灰度图进行分析
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 清晰度评估（拉普拉斯方差）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            clarity_score = float(min(1.0, laplacian_var / 1000.0))  # 归一化并转换为Python float

            # 对比度评估
            contrast_score = float(gray.std() / 128.0)  # 归一化到0-1并转换为Python float
            contrast_score = min(1.0, contrast_score)

            # 噪声水平评估（高频成分）
            blur = cv2.GaussianBlur(gray, (5, 5), 0)
            noise = cv2.absdiff(gray, blur)
            noise_level = float(noise.mean() / 255.0)  # 转换为Python float
            
            # 文本检测（简单的边缘密度检测）
            edges = cv2.Canny(gray, 50, 150)
            edge_density = float(np.sum(edges > 0) / (w * h))
            has_text = bool(edge_density > 0.05)  # 阈值可调，转换为Python bool
            
            # 综合质量评分
            quality_score = float(clarity_score * 0.4 +
                                contrast_score * 0.3 +
                                (1 - noise_level) * 0.2 +
                                (0.1 if has_text else 0.0))
            
            # 生成建议
            recommendations = []
            if clarity_score < 0.3:
                recommendations.append("图像模糊，建议锐化处理")
            if contrast_score < 0.3:
                recommendations.append("对比度低，建议增强对比度")
            if noise_level > 0.3:
                recommendations.append("噪声较多，建议降噪处理")
            if w > self.max_image_size[0] or h > self.max_image_size[1]:
                recommendations.append("图像尺寸过大，建议缩放")
            
            return {
                'resolution': resolution,
                'file_size': file_size,
                'format': format_type,
                'color_mode': color_mode,
                'quality_score': quality_score,
                'has_text': has_text,
                'clarity_score': clarity_score,
                'contrast_score': contrast_score,
                'noise_level': noise_level,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"图像质量评估失败: {str(e)}")
            # 返回默认质量报告
            return {
                'resolution': (0, 0),
                'file_size': 0,
                'format': "unknown",
                'color_mode': "unknown",
                'quality_score': 0.0,
                'has_text': False,
                'clarity_score': 0.0,
                'contrast_score': 0.0,
                'noise_level': 1.0,
                'recommendations': ["质量评估失败"]
            }
    
    def _preprocess_image(self, image: np.ndarray, quality_report: Dict[str, Any]) -> np.ndarray:
        """执行图像预处理"""
        try:
            processed = image.copy()
            
            # 1. 尺寸调整
            h, w = processed.shape[:2]
            max_w, max_h = self.max_image_size
            
            if w > max_w or h > max_h:
                scale = min(max_w / w, max_h / h)
                new_w, new_h = int(w * scale), int(h * scale)
                processed = cv2.resize(processed, (new_w, new_h), interpolation=cv2.INTER_AREA)
                logger.info(f"图像缩放: {(w, h)} -> {(new_w, new_h)}")
            
            if not self.enable_enhancement:
                return processed
            
            # 2. 根据质量报告进行增强
            if quality_report.get('noise_level', 0) > 0.3:
                # 降噪处理
                processed = cv2.GaussianBlur(processed, (3, 3), 0)
                logger.debug("应用降噪处理")

            if quality_report.get('contrast_score', 1.0) < 0.3:
                # 对比度增强
                if len(processed.shape) == 3:
                    gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
                else:
                    gray = processed.copy()
                
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                enhanced = clahe.apply(gray)
                
                if len(processed.shape) == 3:
                    # 将增强结果应用回彩色图像
                    processed = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
                else:
                    processed = enhanced
                
                logger.debug("应用对比度增强")
            
            if quality_report.get('clarity_score', 1.0) < 0.3:
                # 锐化处理
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                processed = cv2.filter2D(processed, -1, kernel)
                logger.debug("应用锐化处理")
            
            return processed
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            return image  # 返回原图像


def create_preprocessing_agent(config: Dict[str, Any] = None) -> ImagePreprocessingAgent:
    """创建图像预处理Agent"""
    return ImagePreprocessingAgent(config)
