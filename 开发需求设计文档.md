
---

# 📄 项目开发文档  
## 考试场景下考生作答图片相似度比对系统  
**版本：1.0**  
**编写日期：2025年4月5日**  
**适用对象：开发团队、测试人员、项目负责人**

---

## 一、项目背景与目标

### 1.1 背景
在大规模考试（如高考、中考、校级统考）中，存在考生抄袭、雷同作答等作弊行为。传统人工阅卷难以高效识别高度相似的主观题答案。因此，需构建一套自动化图像相似度比对系统，基于考生作答图片进行智能分析，辅助识别雷同卷。

### 1.2 目标
- 实现对考生作答图片的自动相似度计算；
- 支持多种题型（主观题、解答题、图文混合题）；
- 以 **OCR 文本语义分析为核心**，结合图像结构特征，提升判断准确性；
- 提供可解释的相似度评分，支持人工复核；
- 系统轻量、可部署于本地服务器，无需依赖云端模型。

---

## 二、系统定位与使用场景

| 项目         | 描述                                                       |
| ------------ | ---------------------------------------------------------- |
| **系统名称** | 考生作答图像雷同检测系统（Answer Similarity Checker, ASC） |
| **核心功能** | 输入两张考生作答图片 → 输出相似度分数（0~1）及各维度得分   |
| **主要用途** | 防作弊检测、阅卷质量监控、教学分析                         |
| **适用题型** | 主观题、论述题、作文、计算题（含少量图形）                 |
| **部署环境** | 本地服务器 / 教育局内网 / 阅卷系统集成模块                 |

---

## 三、技术架构设计

### 3.1 整体架构图

```
+------------------+     +---------------------+
|  考生作答图片     | --> | 图像预处理模块       |
+------------------+     +----------+----------+
                                     |
                                     v
        +-----------------------------------------------------------------+
        |                         核心比对引擎                            |
        +----------------+----------------+----------------+---------------+
                         |                |                |               |
                         v                v                v               v
             像素级相似度(SSIM)   感知哈希(pHash)    特征匹配(SIFT)   OCR+文本语义分析
                         |                |                |               |
                         +----------------+----------------+---------------+
                                             |
                                             v
                                      多模态融合打分
                                             |
                                             v
                                   最终相似度结果 + 详细报告
```

### 3.2 技术栈

| 类别     | 技术选型                                           |
| -------- | -------------------------------------------------- |
| 编程语言 | Python 3.11+                                       |
| 图像处理 | OpenCV、Pillow、scikit-image                       |
| OCR 引擎 | PaddleOCR（中文优化，支持手写）                    |
| 文本处理 | jieba、scikit-learn、sentence-transformers（可选） |
| 深度学习 | 仅用于 OCR 和 BERT 语义（可关闭）                  |
| 部署方式 | Flask API                                          |

---

## 四、功能模块说明

### 4.1 模块清单

| 模块                    | 功能                                      | 是否必需 |
| ----------------------- | ----------------------------------------- | -------- |
| `preprocess/`           | 图像标准化（灰度、去噪、尺寸归一化）      | ✅ 必需   |
| `pixel_similarity.py`   | SSIM、MSE 计算整体布局相似性              | ✅        |
| `hash_similarity.py`    | pHash 实现快速初筛                        | ✅        |
| `feature_similarity.py` | SIFT 特征点匹配，用于图形/公式结构比对    | ✅        |
| `ocr_similarity.py`     | OCR 提取 + 多种文本相似度算法（核心模块） | ✅        |
| `fusion/scorer.py`      | 多维度加权融合，输出最终分数              | ✅        |
| `main.py`               | 主流程控制入口                            | ✅        |


---

### 4.2 核心模块详细说明

#### ✅ `ocr_similarity.py` —— 内容语义理解核心

| 功能       | 说明                                                                                                                                        |
| ---------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| OCR 引擎   | 使用 PaddleOCR，支持中文印刷体与手写体识别                                                                                                  |
| 文本清洗   | 去除标点、停用词、分词处理（jieba）                                                                                                         |
| 相似度算法 | 支持四种模式：<br>1. TF-IDF + 余弦相似度（推荐）<br>2. Jaccard 集合相似度<br>3. 编辑距离（Levenshtein）<br>4. BERT 语义相似度（高精度可选） |
| 可配置性   | 可通过参数选择算法，适应不同题型                                                                                                            |

#### ✅ `fusion/scorer.py` —— 多模态融合策略

根据题型动态调整权重：

| 题型                  | SSIM | pHash | SIFT | OCR |
| --------------------- | ---- | ----- | ---- | --- |
| 主观题（作文/论述）   | 5%   | 5%    | 10%  | 80% |
| 图形题（几何/函数图） | 10%  | 10%   | 60%  | 20% |
| 混合题（图文并茂）    | 20%  | 20%   | 30%  | 30% |

> ✅ 支持外部配置文件 `.yaml` 或数据库配置

---

## 五、接口设计

### 5.1 函数级接口

```python
def compare_two_answers(
    img_path1: str,
    img_path2: str,
    question_type: str = 'subjective',
    ocr_method: str = 'tfidf'
) -> (float, dict):
    """
    比对两张作答图片
    返回：(最终相似度, 各模块得分字典)
    """
```

### 5.2 API 接口（Fastapi）

```json
POST /api/similarity
{
  "image1": "base64_string_or_path",
  "image2": "base64_string_or_path",
  "question_type": "subjective",
  "ocr_method": "tfidf"
}

响应：
{
  "similarity": 0.92,
  "details": {
    "ssim": 0.78,
    "phash": 0.85,
    "sift": 0.60,
    "ocr": 0.95
  },
  "warning": false
}
```

---

## 六、数据流程说明

```mermaid
graph TD
    A[输入两张作答图片] --> B[图像预处理]
    B --> C1[SSIM/MSE]
    B --> C2[pHash]
    B --> C3[SIFT特征匹配]
    B --> C4[OCR文本提取]
    C4 --> D[文本清洗]
    D --> E[选择相似度算法]
    E --> F[生成OCR相似度]
    C1 --> G
    C2 --> G
    C3 --> G
    F --> G
    G[多维度融合打分]
    G --> H[输出最终相似度 & 报告]
```

---

## 七、性能与准确率预期

| 指标         | 预期值                                           |
| ------------ | ------------------------------------------------ |
| 单次比对耗时 | < 3 秒（普通PC，关闭BERT）<br>< 8 秒（启用BERT） |
| OCR 准确率   | > 85%（清晰手写体）<br>~95%（印刷体）            |
| 雷同卷召回率 | > 90%（人工标注测试集）                          |
| 误报率       | < 5%（设置阈值 0.85）                            |
| 支持并发     | 可通过多进程/Flask 扩展至 10+ 并发               |

---



## 八、后续优化方向

| 方向         | 说明                                |
| ------------ | ----------------------------------- |
| 雷同定位高亮 | 在原图上标出相似段落或区域          |
| 批量比对模式 | 支持 N×N 矩阵比对，生成雷同关系图   |
| 自定义停用词 | 支持学校添加常见表达停用词          |
| 模型微调     | 使用历史雷同卷微调 OCR 或 BERT 模型 |
| 阈值自学习   | 基于历史数据自动推荐判定阈值        |



# 补充待开发的功能
1. 一个用于将传入的试题作答图片（时序图、流程图等软件领域的图）进行多模态模型语义分析，最终将图语义信息返回的接口

# 补充开发功能
## 常见错误分类
1. set_std  端点接口： 输出的结果的添加错误分析字段
2. 错误分类：大模型分类  固定标签分类 单标签分类
3. 
