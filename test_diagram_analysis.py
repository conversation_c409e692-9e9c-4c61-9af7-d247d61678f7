"""
图形语义分析系统测试

测试图形到Mermaid转换功能
"""

import base64
import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io
import numpy as np
import cv2


def create_test_flowchart():
    """创建测试用的流程图"""
    # 创建一个简单的流程图图像
    img = Image.new('RGB', (800, 600), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制流程图元素
    # 开始节点（圆形）
    draw.ellipse([100, 50, 200, 100], outline='black', width=2)
    draw.text((130, 70), "开始", fill='black')
    
    # 处理节点（矩形）
    draw.rectangle([100, 150, 200, 200], outline='black', width=2)
    draw.text((120, 170), "处理数据", fill='black')
    
    # 决策节点（菱形）
    diamond = [(150, 250), (200, 300), (150, 350), (100, 300)]
    draw.polygon(diamond, outline='black', width=2)
    draw.text((130, 295), "判断", fill='black')
    
    # 结束节点（圆形）
    draw.ellipse([100, 450, 200, 500], outline='black', width=2)
    draw.text((130, 470), "结束", fill='black')
    
    # 连接线
    draw.line([(150, 100), (150, 150)], fill='black', width=2)  # 开始到处理
    draw.line([(150, 200), (150, 250)], fill='black', width=2)  # 处理到决策
    draw.line([(150, 350), (150, 450)], fill='black', width=2)  # 决策到结束
    
    # 添加箭头（简化）
    draw.polygon([(145, 145), (155, 145), (150, 155)], fill='black')
    draw.polygon([(145, 245), (155, 245), (150, 255)], fill='black')
    draw.polygon([(145, 445), (155, 445), (150, 455)], fill='black')
    
    return img


def create_test_sequence_diagram():
    """创建测试用的时序图"""
    img = Image.new('RGB', (800, 600), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制参与者
    draw.rectangle([100, 50, 200, 100], outline='black', width=2)
    draw.text((130, 70), "用户", fill='black')
    
    draw.rectangle([400, 50, 500, 100], outline='black', width=2)
    draw.text((430, 70), "系统", fill='black')
    
    # 生命线
    draw.line([(150, 100), (150, 500)], fill='black', width=1)
    draw.line([(450, 100), (450, 500)], fill='black', width=1)
    
    # 消息线
    draw.line([(150, 200), (450, 200)], fill='black', width=2)
    draw.text((250, 180), "登录请求", fill='black')
    draw.polygon([(445, 195), (455, 200), (445, 205)], fill='black')
    
    draw.line([(450, 250), (150, 250)], fill='black', width=2)
    draw.text((250, 230), "登录响应", fill='black')
    draw.polygon([(155, 245), (145, 250), (155, 255)], fill='black')
    
    return img


def image_to_base64(image):
    """将PIL图像转换为base64字符串"""
    buffer = io.BytesIO()
    image.save(buffer, format='JPEG')
    image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    return image_base64


def test_diagram_analysis_api():
    """测试图形分析API"""
    # API配置
    api_url = "http://localhost:7862/api/diagram_analysis"
    
    # 测试用例
    test_cases = [
        {
            "name": "流程图测试",
            "image": create_test_flowchart(),
            "expected_type": "flowchart"
        },
        {
            "name": "时序图测试", 
            "image": create_test_sequence_diagram(),
            "expected_type": "sequence"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        
        # 转换图像为base64
        image_base64 = image_to_base64(test_case['image'])
        
        # 构建请求
        request_data = {
            "image": image_base64,
            "output_format": "mermaid",
            "diagram_type": "auto",
            "enable_preprocessing": True
        }
        
        try:
            # 发送请求
            response = requests.post(api_url, json=request_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print(f"请求成功: {result['msg']}")
                print(f"识别类型: {result['data']['diagram_type']}")
                print(f"处理时间: {result['cost_time']}秒")
                print(f"Mermaid有效性: {result['data']['is_valid']}")
                
                if result['data']['mermaid_code']:
                    print("生成的Mermaid代码:")
                    print("```mermaid")
                    print(result['data']['mermaid_code'])
                    print("```")
                else:
                    print("未生成Mermaid代码")
                    
            else:
                print(f"请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"测试失败: {str(e)}")


def test_local_analysis():
    """测试本地分析功能"""
    try:
        from diagram_analysis import analyze_diagram_to_mermaid
        
        print("\n=== 本地分析测试 ===")
        
        # 创建测试图像
        test_image = create_test_flowchart()
        
        # 转换为numpy数组
        image_array = np.array(test_image)
        image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        
        # 执行分析
        result = analyze_diagram_to_mermaid(image_bgr, diagram_type='auto')
        
        print(f"分析成功: {result['success']}")
        print(f"识别类型: {result['diagram_type']}")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        print(f"Mermaid有效性: {result['is_valid']}")
        
        if result['mermaid_code']:
            print("生成的Mermaid代码:")
            print("```mermaid")
            print(result['mermaid_code'])
            print("```")
        else:
            print("未生成Mermaid代码")
            
    except ImportError as e:
        print(f"本地模块导入失败: {str(e)}")
    except Exception as e:
        print(f"本地分析失败: {str(e)}")


def test_all_output_formats():
    """测试所有输出格式"""
    api_url = "http://localhost:7862/api/diagram_analysis"
    
    # 创建测试图像
    test_image = create_test_flowchart()
    image_base64 = image_to_base64(test_image)
    
    # 测试不同输出格式
    formats = ['mermaid', 'json', 'text', 'all']
    
    for output_format in formats:
        print(f"\n=== 测试输出格式: {output_format} ===")
        
        request_data = {
            "image": image_base64,
            "output_format": output_format,
            "diagram_type": "auto",
            "enable_preprocessing": True
        }
        
        try:
            response = requests.post(api_url, json=request_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print(f"请求成功: {result['msg']}")
                print(f"处理时间: {result['cost_time']}秒")
                print("返回数据结构:")
                print(json.dumps(result['data'], indent=2, ensure_ascii=False))
            else:
                print(f"请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    print("图形语义分析系统测试")
    print("=" * 50)
    
    # 测试本地分析功能
    test_local_analysis()
    
    # 测试API接口
    print("\n" + "=" * 50)
    print("开始API测试...")
    test_diagram_analysis_api()
    
    # 测试所有输出格式
    print("\n" + "=" * 50)
    print("测试所有输出格式...")
    test_all_output_formats()
    
    print("\n测试完成！")
