"""
简化的工作流测试

测试基本的工作流功能
"""

import time
import base64
import io
from PIL import Image, ImageDraw
from diagram_analysis.langgraph import get_workflow_instance
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("test_simple", CONFIG)


def create_simple_test_image() -> str:
    """创建简单的测试图像"""
    try:
        # 创建一个简单的图像
        img = Image.new('RGB', (400, 300), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制一个简单的矩形
        draw.rectangle([50, 50, 150, 100], outline='black', width=2)
        draw.text((70, 70), "Test", fill='black')
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
        
    except Exception as e:
        logger.error(f"创建测试图像失败: {str(e)}")
        return ""


def test_basic_workflow():
    """测试基本工作流"""
    logger.info("开始基本工作流测试")
    
    try:
        # 创建测试图像
        test_image = create_simple_test_image()
        if not test_image:
            raise Exception("测试图像创建失败")
        
        # 获取工作流实例
        workflow = get_workflow_instance()
        
        # 构建请求参数
        request_params = {
            'output_format': 'mermaid',
            'diagram_type': 'auto'
        }
        
        # 执行工作流
        start_time = time.time()
        result = workflow.run_sync(test_image, request_params, "simple_test")
        duration = time.time() - start_time
        
        # 输出结果
        logger.info(f"工作流执行完成，耗时: {duration:.2f}秒")
        logger.info(f"执行结果: {result}")
        
        # 检查结果
        if result.get('success', False):
            logger.info("✓ 基本工作流测试通过")
        else:
            logger.warning(f"工作流执行失败: {result.get('error', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"基本工作流测试失败: {str(e)}")
        return None


if __name__ == "__main__":
    test_basic_workflow()
