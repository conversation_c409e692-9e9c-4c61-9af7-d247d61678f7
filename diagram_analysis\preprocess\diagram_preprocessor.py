"""
图形预处理器

专门针对软件领域图形（时序图、流程图等）的预处理
"""

import cv2
import numpy as np
from typing import Union, Tuple, Optional, Dict, Any
import base64
from PIL import Image
import io
from skimage import filters, morphology, measure
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("diagram_preprocessor", CONFIG)


class DiagramPreprocessor:
    """图形预处理器"""
    
    def __init__(self, config: dict = None):
        """
        初始化图形预处理器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {}).get('processing', {})
        
        # 图像尺寸配置
        self.max_size = self.config.get('max_image_size', [2048, 2048])
        self.min_size = [400, 300]
        
        # 预处理参数
        self.gaussian_kernel = (5, 5)
        self.morphology_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        self.edge_threshold = (50, 150)
        
    def preprocess_diagram(self, image: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        预处理图形图像
        
        Args:
            image: 输入图像（base64字符串、numpy数组或PIL图像）
            
        Returns:
            dict: 预处理结果
        """
        try:
            logger.info("开始图形预处理")
            
            # 加载和标准化图像
            original_image = self._load_image(image)
            if original_image is None:
                raise ValueError("无法加载图像")
            
            # 尺寸调整
            resized_image = self._resize_image(original_image)
            
            # 图像增强
            enhanced_image = self._enhance_image(resized_image)
            
            # 噪声去除
            denoised_image = self._denoise_image(enhanced_image)
            
            # 边缘检测
            edges = self._detect_edges(denoised_image)
            
            # 文本区域检测
            text_regions = self._detect_text_regions(denoised_image)
            
            # 几何形状检测
            shapes = self._detect_basic_shapes(denoised_image)
            
            # 连线检测
            lines = self._detect_lines(edges)
            
            results = {
                'original_image': original_image,
                'processed_image': denoised_image,
                'enhanced_image': enhanced_image,
                'edges': edges,
                'text_regions': text_regions,
                'shapes': shapes,
                'lines': lines,
                'image_info': {
                    'original_size': original_image.shape[:2],
                    'processed_size': denoised_image.shape[:2],
                    'channels': denoised_image.shape[2] if len(denoised_image.shape) > 2 else 1
                }
            }
            
            logger.info(f"图形预处理完成: 检测到{len(text_regions)}个文本区域, {len(shapes)}个形状, {len(lines)}条线")
            return results
            
        except Exception as e:
            logger.error(f"图形预处理失败: {str(e)}")
            raise
    
    def _load_image(self, image: Union[str, np.ndarray, Image.Image]) -> Optional[np.ndarray]:
        """加载图像"""
        try:
            if isinstance(image, str):
                # base64字符串
                if image.startswith('data:image'):
                    image = image.split(',')[1]
                image_data = base64.b64decode(image)
                pil_image = Image.open(io.BytesIO(image_data))
                return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            elif isinstance(image, Image.Image):
                # PIL图像
                return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            elif isinstance(image, np.ndarray):
                # numpy数组
                if len(image.shape) == 3 and image.shape[2] == 3:
                    return image
                elif len(image.shape) == 2:
                    return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                else:
                    return image
            else:
                return None
        except Exception as e:
            logger.error(f"图像加载失败: {str(e)}")
            return None
    
    def _resize_image(self, image: np.ndarray) -> np.ndarray:
        """调整图像尺寸"""
        h, w = image.shape[:2]
        max_h, max_w = self.max_size
        
        # 如果图像过大，按比例缩放
        if h > max_h or w > max_w:
            scale = min(max_h / h, max_w / w)
            new_h, new_w = int(h * scale), int(w * scale)
            image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        # 如果图像过小，放大
        elif h < self.min_size[1] or w < self.min_size[0]:
            scale = max(self.min_size[1] / h, self.min_size[0] / w)
            new_h, new_w = int(h * scale), int(w * scale)
            image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        
        return image
    
    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """图像增强"""
        # 转换为灰度图进行处理
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 对比度增强 (CLAHE)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # 转回彩色图像
        if len(image.shape) == 3:
            enhanced_color = cv2.cvtColor(sharpened, cv2.COLOR_GRAY2BGR)
        else:
            enhanced_color = sharpened
        
        return enhanced_color
    
    def _denoise_image(self, image: np.ndarray) -> np.ndarray:
        """噪声去除"""
        # 高斯滤波去噪
        denoised = cv2.GaussianBlur(image, self.gaussian_kernel, 0)
        
        # 形态学操作去除小噪点
        if len(image.shape) == 3:
            gray = cv2.cvtColor(denoised, cv2.COLOR_BGR2GRAY)
        else:
            gray = denoised.copy()
        
        # 开运算去除小噪点
        opened = cv2.morphologyEx(gray, cv2.MORPH_OPEN, self.morphology_kernel)
        
        # 转回彩色图像
        if len(image.shape) == 3:
            result = cv2.cvtColor(opened, cv2.COLOR_GRAY2BGR)
        else:
            result = opened
        
        return result
    
    def _detect_edges(self, image: np.ndarray) -> np.ndarray:
        """边缘检测"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Canny边缘检测
        edges = cv2.Canny(gray, self.edge_threshold[0], self.edge_threshold[1])
        
        return edges
    
    def _detect_text_regions(self, image: np.ndarray) -> list:
        """检测文本区域"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 使用MSER检测文本区域
            mser = cv2.MSER_create()
            regions, _ = mser.detectRegions(gray)
            
            # 过滤和合并文本区域
            text_regions = []
            for region in regions:
                x, y, w, h = cv2.boundingRect(region.reshape(-1, 1, 2))
                # 过滤太小或太大的区域
                if 10 < w < gray.shape[1] * 0.8 and 5 < h < gray.shape[0] * 0.3:
                    text_regions.append({'x': x, 'y': y, 'width': w, 'height': h})
            
            return text_regions
            
        except Exception as e:
            logger.warning(f"文本区域检测失败: {str(e)}")
            return []
    
    def _detect_basic_shapes(self, image: np.ndarray) -> list:
        """检测基本几何形状"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            shapes = []
            for contour in contours:
                # 过滤太小的轮廓
                if cv2.contourArea(contour) < 100:
                    continue
                
                # 轮廓近似
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 获取边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 判断形状类型
                shape_type = self._classify_shape(approx, contour)
                
                shapes.append({
                    'type': shape_type,
                    'contour': contour,
                    'approx': approx,
                    'bbox': {'x': x, 'y': y, 'width': w, 'height': h},
                    'area': cv2.contourArea(contour)
                })
            
            return shapes
            
        except Exception as e:
            logger.warning(f"形状检测失败: {str(e)}")
            return []
    
    def _classify_shape(self, approx: np.ndarray, contour: np.ndarray) -> str:
        """分类几何形状"""
        vertices = len(approx)
        
        if vertices == 3:
            return 'triangle'
        elif vertices == 4:
            # 检查是否为矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            if 0.8 <= aspect_ratio <= 1.2:
                return 'square'
            else:
                return 'rectangle'
        elif vertices > 8:
            # 检查是否为圆形
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > 0.7:
                    return 'circle'
            return 'ellipse'
        else:
            return 'polygon'
    
    def _detect_lines(self, edges: np.ndarray) -> list:
        """检测直线"""
        try:
            # 使用霍夫变换检测直线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                                   minLineLength=30, maxLineGap=10)
            
            if lines is None:
                return []
            
            line_list = []
            for line in lines:
                x1, y1, x2, y2 = line[0]
                length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                angle = np.arctan2(y2-y1, x2-x1) * 180 / np.pi
                
                line_list.append({
                    'start': {'x': x1, 'y': y1},
                    'end': {'x': x2, 'y': y2},
                    'length': length,
                    'angle': angle
                })
            
            return line_list
            
        except Exception as e:
            logger.warning(f"直线检测失败: {str(e)}")
            return []
