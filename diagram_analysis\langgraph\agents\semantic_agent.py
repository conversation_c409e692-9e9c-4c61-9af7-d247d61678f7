"""
语义提取Agent

根据图形类型执行相应的Mermaid转换和语义分析的Agent
"""

import time
import base64
import io
import cv2
import numpy as np
import json
import re
import requests
from PIL import Image
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage

from ..state_management import (
    DiagramAnalysisState, AgentReport, AgentType, ProcessingStep,
    SemanticExtractionResult, state_manager
)
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("semantic_agent", CONFIG)


class SemanticExtractionAgent:
    """语义提取Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化语义提取Agent
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        self.vision_config = self.config.get('vision_model', {})
        self.agent_type = AgentType.EXTRACTOR
        
        # 多模态模型配置
        self.base_url = self.vision_config.get('base_url', '')
        self.api_key = self.vision_config.get('api_key', '')
        self.model_name = self.vision_config.get('model_name', 'Qwen2-VL-7B')
        
        # 提取策略
        self.extraction_prompts = self._build_extraction_prompts()
        
    def process(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """
        执行语义提取
        
        Args:
            state: 当前状态
            
        Returns:
            DiagramAnalysisState: 更新后的状态
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始语义提取: session_id={state['session_id']}")
            
            # 更新处理步骤
            state = state_manager.update_step(state, ProcessingStep.SEMANTIC_EXTRACTION)
            
            # 获取必要数据
            preprocessed_image = state.get('preprocessed_image')
            classification_result = state.get('classification_result')
            
            if preprocessed_image is None:
                raise ValueError("未找到预处理后的图像")
            if classification_result is None:
                raise ValueError("未找到分类结果")
            
            # 获取输出格式要求
            request_params = state.get('request_params', {})
            output_format = request_params.get('output_format', 'mermaid')
            
            # 执行语义提取
            extraction_result = self._extract_semantics(
                preprocessed_image, 
                classification_result.diagram_type,
                output_format
            )
            
            # 更新状态
            state = state_manager.set_extraction_result(state, extraction_result)
            
            # 生成Agent报告
            processing_time = time.time() - start_time
            report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.SEMANTIC_EXTRACTION,
                success=True,
                processing_time=processing_time,
                message=f"语义提取完成: {extraction_result.elements_count}个元素, {extraction_result.relationships_count}个关系",
                data={
                    "diagram_type": classification_result.diagram_type,
                    "output_format": output_format,
                    "mermaid_valid": extraction_result.mermaid_valid,
                    "extraction_confidence": extraction_result.extraction_confidence,
                    "elements_count": extraction_result.elements_count,
                    "relationships_count": extraction_result.relationships_count
                }
            )
            
            state = state_manager.add_agent_report(state, report)
            
            # 添加消息到状态
            message = AIMessage(
                content=f"语义提取完成。提取了{extraction_result.elements_count}个元素和"
                       f"{extraction_result.relationships_count}个关系，"
                       f"Mermaid代码有效性: {extraction_result.mermaid_valid}"
            )
            state["messages"].append(message)
            
            # 设置下一个Agent
            state["next_agent"] = "integrator"
            
            logger.info(f"语义提取成功: 耗时={processing_time:.2f}s")
            return state
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"语义提取失败: {str(e)}"
            logger.error(error_msg)
            
            # 记录错误报告
            error_report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.SEMANTIC_EXTRACTION,
                success=False,
                processing_time=processing_time,
                message="语义提取失败",
                error=str(e)
            )
            
            state = state_manager.add_agent_report(state, error_report)
            state = state_manager.set_error(state, error_msg, self.agent_type)
            
            return state
    
    def _extract_semantics(self, image: np.ndarray, diagram_type: str, output_format: str) -> SemanticExtractionResult:
        """执行语义提取"""
        try:
            # 生成Mermaid代码
            mermaid_result = self._extract_mermaid(image, diagram_type)
            
            # 生成结构化数据
            structured_result = self._extract_structured_data(image, diagram_type)
            
            # 生成文本描述
            text_result = self._extract_text_description(image, diagram_type)
            
            # 计算质量指标
            quality_metrics = self._calculate_quality_metrics(mermaid_result, structured_result)
            
            return SemanticExtractionResult(
                mermaid_code=mermaid_result.get('code', ''),
                mermaid_valid=mermaid_result.get('valid', False),
                structured_data=structured_result.get('data', {}),
                text_description=text_result.get('description', ''),
                extraction_confidence=quality_metrics.get('overall_confidence', 0.5),
                elements_count=quality_metrics.get('elements_count', 0),
                relationships_count=quality_metrics.get('relationships_count', 0),
                quality_metrics=quality_metrics
            )
            
        except Exception as e:
            logger.error(f"语义提取执行失败: {str(e)}")
            return SemanticExtractionResult(
                mermaid_code='',
                mermaid_valid=False,
                structured_data={},
                text_description='',
                extraction_confidence=0.0,
                elements_count=0,
                relationships_count=0,
                quality_metrics={'error': str(e)}
            )
    
    def _extract_mermaid(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取Mermaid代码"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['mermaid'].get(
                diagram_type, 
                self.extraction_prompts['mermaid']['general']
            )
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            # 解析Mermaid代码
            mermaid_code = self._extract_mermaid_code(response)
            
            # 验证Mermaid语法
            is_valid = self._validate_mermaid_syntax(mermaid_code, diagram_type)
            
            return {
                'code': mermaid_code,
                'valid': is_valid,
                'raw_response': response
            }
            
        except Exception as e:
            logger.error(f"Mermaid提取失败: {str(e)}")
            return {
                'code': '',
                'valid': False,
                'raw_response': '',
                'error': str(e)
            }
    
    def _extract_structured_data(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取结构化数据"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['json'].get(
                diagram_type,
                self.extraction_prompts['json']['general']
            )
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            # 解析JSON数据
            json_data = self._extract_json_data(response)
            
            return {
                'data': json_data,
                'raw_response': response
            }
            
        except Exception as e:
            logger.error(f"结构化数据提取失败: {str(e)}")
            return {
                'data': {},
                'raw_response': '',
                'error': str(e)
            }
    
    def _extract_text_description(self, image: np.ndarray, diagram_type: str) -> Dict[str, Any]:
        """提取文本描述"""
        try:
            # 获取对应的提示词
            prompt = self.extraction_prompts['text'].get(
                diagram_type,
                self.extraction_prompts['text']['general']
            )
            
            # 调用多模态模型
            response = self._call_vision_model(image, prompt)
            
            return {
                'description': response.strip(),
                'raw_response': response
            }
            
        except Exception as e:
            logger.error(f"文本描述提取失败: {str(e)}")
            return {
                'description': '',
                'raw_response': '',
                'error': str(e)
            }
    
    def _call_vision_model(self, image: np.ndarray, prompt: str) -> str:
        """调用多模态模型"""
        try:
            # 将图像转换为base64
            image_base64 = self._image_to_base64(image)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 4096,
                "temperature": 0.1
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                raise Exception(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"多模态模型调用失败: {str(e)}")
            raise
    
    def _build_extraction_prompts(self) -> Dict[str, Dict[str, str]]:
        """构建提取提示词"""
        return {
            'mermaid': {
                'flowchart': """请分析这张流程图，将其转换为Mermaid flowchart格式。

要求：
1. 识别所有节点（开始、结束、处理、决策等）
2. 识别所有连接关系和流向
3. 提取节点中的文本内容
4. 使用标准的Mermaid语法

请直接返回Mermaid代码，格式如下：
```mermaid
flowchart TD
    节点定义和连接关系
```""",
                
                'sequence_diagram': """请分析这张时序图，将其转换为Mermaid sequence diagram格式。

要求：
1. 识别所有参与者/对象
2. 识别消息传递的顺序和方向
3. 提取消息内容和类型
4. 保持时序关系

请直接返回Mermaid代码，格式如下：
```mermaid
sequenceDiagram
    参与者定义和消息传递
```""",
                
                'general': """请分析这张软件工程图形，将其转换为合适的Mermaid格式。

要求：
1. 首先判断图形类型（流程图、时序图、类图等）
2. 识别图中的关键元素和关系
3. 转换为对应的Mermaid语法
4. 保持原图的逻辑结构

请直接返回Mermaid代码，以```mermaid开始，```结束。"""
            },
            
            'json': {
                'general': """请分析这张软件工程图形，提取其结构化信息为JSON格式。

要求：
1. 识别图形类型
2. 提取所有节点/元素及其属性
3. 提取所有连接关系
4. 提取文本内容

请返回JSON格式，包含以下结构：
{
    "type": "图形类型",
    "elements": [
        {
            "id": "元素ID",
            "type": "元素类型",
            "text": "文本内容",
            "properties": {}
        }
    ],
    "connections": [
        {
            "from": "起始元素ID",
            "to": "目标元素ID",
            "type": "连接类型",
            "label": "连接标签"
        }
    ]
}"""
            },
            
            'text': {
                'general': """请分析这张软件工程图形，用自然语言描述其内容和逻辑。

要求：
1. 描述图形的整体结构和目的
2. 说明主要的流程或关系
3. 解释关键的业务逻辑
4. 使用清晰易懂的语言

请提供详细的文字描述。"""
            }
        }

    def _extract_mermaid_code(self, response: str) -> str:
        """从响应中提取Mermaid代码"""
        # 查找```mermaid代码块
        pattern = r'```mermaid\s*(.*?)\s*```'
        match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(1).strip()
        
        # 如果没有找到代码块，查找其他可能的格式
        pattern = r'```\s*(.*?)\s*```'
        match = re.search(pattern, response, re.DOTALL)
        
        if match:
            code = match.group(1).strip()
            # 检查是否包含mermaid关键词
            if any(keyword in code.lower() for keyword in ['flowchart', 'sequencediagram', 'classdiagram']):
                return code
        
        # 如果都没有找到，返回原始响应
        return response.strip()
    
    def _extract_json_data(self, response: str) -> Dict[str, Any]:
        """从响应中提取JSON数据"""
        try:
            # 查找JSON代码块
            if '{' in response and '}' in response:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {}
        except Exception as e:
            logger.warning(f"JSON解析失败: {str(e)}")
            return {}
    
    def _validate_mermaid_syntax(self, mermaid_code: str, diagram_type: str) -> bool:
        """验证Mermaid语法"""
        try:
            # 基本语法检查
            if not mermaid_code.strip():
                return False
            
            # 检查是否包含对应的图形类型关键词
            type_keywords = {
                'flowchart': ['flowchart'],
                'sequence_diagram': ['sequencediagram'],
                'class_diagram': ['classdiagram']
            }
            
            keywords = type_keywords.get(diagram_type, [])
            code_lower = mermaid_code.lower()
            
            if keywords and not any(keyword in code_lower for keyword in keywords):
                return False
            
            # 检查基本的连接语法
            connection_patterns = ['-->', '->', '-->>', '--', '-.->', '-.->']
            has_connections = any(pattern in mermaid_code for pattern in connection_patterns)
            
            return has_connections or 'participant' in code_lower or 'class' in code_lower
            
        except Exception as e:
            logger.warning(f"Mermaid语法验证失败: {str(e)}")
            return False
    
    def _calculate_quality_metrics(self, mermaid_result: Dict[str, Any], structured_result: Dict[str, Any]) -> Dict[str, float]:
        """计算质量指标"""
        try:
            metrics = {}
            
            # Mermaid代码质量
            mermaid_code = mermaid_result.get('code', '')
            metrics['mermaid_length'] = len(mermaid_code)
            metrics['mermaid_valid'] = 1.0 if mermaid_result.get('valid', False) else 0.0
            
            # 结构化数据质量
            structured_data = structured_result.get('data', {})
            elements = structured_data.get('elements', [])
            connections = structured_data.get('connections', [])
            
            metrics['elements_count'] = len(elements)
            metrics['relationships_count'] = len(connections)
            
            # 综合置信度
            if metrics['mermaid_valid'] > 0 and metrics['elements_count'] > 0:
                metrics['overall_confidence'] = 0.8
            elif metrics['mermaid_valid'] > 0 or metrics['elements_count'] > 0:
                metrics['overall_confidence'] = 0.6
            else:
                metrics['overall_confidence'] = 0.3
            
            return metrics
            
        except Exception as e:
            logger.error(f"质量指标计算失败: {str(e)}")
            return {'overall_confidence': 0.0, 'elements_count': 0, 'relationships_count': 0}
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """将图像转换为base64字符串"""
        # 转换为RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        
        # 转换为base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64


def create_semantic_agent(config: Dict[str, Any] = None) -> SemanticExtractionAgent:
    """创建语义提取Agent"""
    return SemanticExtractionAgent(config)
