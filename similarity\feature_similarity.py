"""
特征匹配相似度计算模块

实现SIFT、ORB等特征点匹配算法，用于图形/公式结构比对
"""

import cv2
import numpy as np
from typing import Dict, Any, Tuple, List, Optional
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("feature_similarity", CONFIG)


class FeatureSimilarityCalculator:
    """特征匹配相似度计算器"""
    
    def __init__(self, config: dict = None):
        """
        初始化特征匹配相似度计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('feature', {})
        self.sift_features = self.config.get('sift_features', 500)
        self.orb_features = self.config.get('orb_features', 500)
        self.match_ratio_threshold = self.config.get('match_ratio_threshold', 0.75)
        self.min_match_count = self.config.get('min_match_count', 10)
        
        # 初始化特征检测器
        self._init_detectors()
    
    def _init_detectors(self):
        """初始化特征检测器"""
        try:
            # SIFT检测器
            self.sift = cv2.SIFT_create(nfeatures=self.sift_features)
            logger.debug("SIFT检测器初始化成功")
        except Exception as e:
            logger.warning(f"SIFT检测器初始化失败: {str(e)}")
            self.sift = None
        
        try:
            # ORB检测器
            self.orb = cv2.ORB_create(nfeatures=self.orb_features)
            logger.debug("ORB检测器初始化成功")
        except Exception as e:
            logger.warning(f"ORB检测器初始化失败: {str(e)}")
            self.orb = None
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
        
        # BF匹配器
        self.bf_matcher = cv2.BFMatcher()
    
    def calculate_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算两张图像的特征匹配相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: 包含各种特征匹配相似度指标的字典
        """
        try:
            # 确保图像为灰度图
            if len(image1.shape) == 3:
                image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
            if len(image2.shape) == 3:
                image2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
            
            results = {}
            
            # SIFT特征匹配
            if self.sift is not None:
                sift_results = self._calculate_sift_similarity(image1, image2)
                results.update(sift_results)
            
            # ORB特征匹配
            if self.orb is not None:
                orb_results = self._calculate_orb_similarity(image1, image2)
                results.update(orb_results)
            
            # 轮廓匹配
            contour_results = self._calculate_contour_similarity(image1, image2)
            results.update(contour_results)
            
            # 角点检测匹配
            corner_results = self._calculate_corner_similarity(image1, image2)
            results.update(corner_results)
            
            logger.debug(f"特征匹配相似度计算完成")
            return results
            
        except Exception as e:
            logger.error(f"特征匹配相似度计算失败: {str(e)}")
            raise
    
    def _calculate_sift_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算SIFT特征匹配相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: SIFT匹配结果
        """
        try:
            # 检测关键点和描述符
            kp1, des1 = self.sift.detectAndCompute(image1, None)
            kp2, des2 = self.sift.detectAndCompute(image2, None)
            
            if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
                return {
                    'sift_similarity': 0.0,
                    'sift_matches': 0,
                    'sift_keypoints': (len(kp1) if kp1 else 0, len(kp2) if kp2 else 0),
                    'sift_good_matches': 0
                }
            
            # 使用FLANN匹配器
            matches = self.flann.knnMatch(des1, des2, k=2)
            
            # 应用Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < self.match_ratio_threshold * n.distance:
                        good_matches.append(m)
            
            # 计算相似度
            total_features = min(len(kp1), len(kp2))
            if total_features > 0:
                similarity = len(good_matches) / total_features
            else:
                similarity = 0.0
            
            return {
                'sift_similarity': float(min(similarity, 1.0)),
                'sift_matches': len(matches),
                'sift_keypoints': (len(kp1), len(kp2)),
                'sift_good_matches': len(good_matches)
            }
            
        except Exception as e:
            logger.warning(f"SIFT特征匹配失败: {str(e)}")
            return {
                'sift_similarity': 0.0,
                'sift_matches': 0,
                'sift_keypoints': (0, 0),
                'sift_good_matches': 0
            }
    
    def _calculate_orb_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算ORB特征匹配相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: ORB匹配结果
        """
        try:
            # 检测关键点和描述符
            kp1, des1 = self.orb.detectAndCompute(image1, None)
            kp2, des2 = self.orb.detectAndCompute(image2, None)
            
            if des1 is None or des2 is None:
                return {
                    'orb_similarity': 0.0,
                    'orb_matches': 0,
                    'orb_keypoints': (len(kp1) if kp1 else 0, len(kp2) if kp2 else 0),
                    'orb_good_matches': 0
                }
            
            # 使用BF匹配器
            matches = self.bf_matcher.knnMatch(des1, des2, k=2)
            
            # 应用Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < self.match_ratio_threshold * n.distance:
                        good_matches.append(m)
            
            # 计算相似度
            total_features = min(len(kp1), len(kp2))
            if total_features > 0:
                similarity = len(good_matches) / total_features
            else:
                similarity = 0.0
            
            return {
                'orb_similarity': float(min(similarity, 1.0)),
                'orb_matches': len(matches),
                'orb_keypoints': (len(kp1), len(kp2)),
                'orb_good_matches': len(good_matches)
            }
            
        except Exception as e:
            logger.warning(f"ORB特征匹配失败: {str(e)}")
            return {
                'orb_similarity': 0.0,
                'orb_matches': 0,
                'orb_keypoints': (0, 0),
                'orb_good_matches': 0
            }
    
    def _calculate_contour_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算轮廓相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: 轮廓匹配结果
        """
        try:
            # 边缘检测
            edges1 = cv2.Canny(image1, 50, 150)
            edges2 = cv2.Canny(image2, 50, 150)
            
            # 查找轮廓
            contours1, _ = cv2.findContours(edges1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            contours2, _ = cv2.findContours(edges2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours1 or not contours2:
                return {
                    'contour_similarity': 0.0,
                    'contour_count': (len(contours1), len(contours2)),
                    'contour_matches': 0
                }
            
            # 计算轮廓匹配
            matches = 0
            total_comparisons = 0
            
            for c1 in contours1:
                if cv2.contourArea(c1) < 100:  # 忽略小轮廓
                    continue
                
                best_match = float('inf')
                for c2 in contours2:
                    if cv2.contourArea(c2) < 100:
                        continue
                    
                    # 使用Hu矩进行轮廓匹配
                    try:
                        match_value = cv2.matchShapes(c1, c2, cv2.CONTOURS_MATCH_I1, 0)
                        best_match = min(best_match, match_value)
                    except:
                        continue
                
                if best_match < 0.5:  # 匹配阈值
                    matches += 1
                total_comparisons += 1
            
            similarity = matches / total_comparisons if total_comparisons > 0 else 0.0
            
            return {
                'contour_similarity': float(similarity),
                'contour_count': (len(contours1), len(contours2)),
                'contour_matches': matches
            }
            
        except Exception as e:
            logger.warning(f"轮廓相似度计算失败: {str(e)}")
            return {
                'contour_similarity': 0.0,
                'contour_count': (0, 0),
                'contour_matches': 0
            }
    
    def _calculate_corner_similarity(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        计算角点相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            
        Returns:
            dict: 角点匹配结果
        """
        try:
            # Harris角点检测
            corners1 = cv2.cornerHarris(image1, 2, 3, 0.04)
            corners2 = cv2.cornerHarris(image2, 2, 3, 0.04)
            
            # 提取角点位置
            corner_points1 = np.argwhere(corners1 > 0.01 * corners1.max())
            corner_points2 = np.argwhere(corners2 > 0.01 * corners2.max())
            
            if len(corner_points1) == 0 or len(corner_points2) == 0:
                return {
                    'corner_similarity': 0.0,
                    'corner_count': (len(corner_points1), len(corner_points2)),
                    'corner_matches': 0
                }
            
            # 简单的角点匹配（基于距离）
            matches = 0
            threshold = 20  # 像素距离阈值
            
            for p1 in corner_points1:
                distances = np.sqrt(np.sum((corner_points2 - p1) ** 2, axis=1))
                if np.min(distances) < threshold:
                    matches += 1
            
            similarity = matches / max(len(corner_points1), len(corner_points2))
            
            return {
                'corner_similarity': float(similarity),
                'corner_count': (len(corner_points1), len(corner_points2)),
                'corner_matches': matches
            }
            
        except Exception as e:
            logger.warning(f"角点相似度计算失败: {str(e)}")
            return {
                'corner_similarity': 0.0,
                'corner_count': (0, 0),
                'corner_matches': 0
            }
    
    def get_similarity_score(self, results: Dict[str, Any]) -> float:
        """
        根据各项特征指标计算综合相似度分数
        
        Args:
            results: 特征匹配相似度计算结果
            
        Returns:
            float: 综合相似度分数 (0-1)
        """
        # 权重配置
        weights = self.config.get('weights', {
            'sift': 0.4,
            'orb': 0.3,
            'contour': 0.2,
            'corner': 0.1
        })
        
        # 计算加权平均
        score = 0.0
        total_weight = 0.0
        
        if 'sift_similarity' in results:
            score += weights['sift'] * results['sift_similarity']
            total_weight += weights['sift']
        
        if 'orb_similarity' in results:
            score += weights['orb'] * results['orb_similarity']
            total_weight += weights['orb']
        
        if 'contour_similarity' in results:
            score += weights['contour'] * results['contour_similarity']
            total_weight += weights['contour']
        
        if 'corner_similarity' in results:
            score += weights['corner'] * results['corner_similarity']
            total_weight += weights['corner']
        
        if total_weight > 0:
            score = score / total_weight
        
        return max(0.0, min(1.0, score))  # 确保分数在[0,1]范围内
