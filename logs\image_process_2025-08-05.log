开始LangGraph多Agent系统测试
开始测试单个工作流执行
LangGraph工作流初始化完成
开始同步执行工作流: session_id=session_1754363750178
执行Supervisor节点: session_id=session_1754363750178
工作流状态更新: step=None
执行预处理节点: session_id=session_1754363750178
开始图像预处理: session_id=session_1754363750178
应用对比度增强
图像预处理成功: 耗时=0.04s
工作流状态更新: step=None
执行Supervisor节点: session_id=session_1754363750178
工作流状态更新: step=None
执行提取节点: session_id=session_1754363750178
开始语义提取: session_id=session_1754363750178
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=session_1754363750178
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
工作流执行完成，耗时: 0.11秒
执行结果: {
  "success": false,
  "error": "Type is not msgpack serializable: ImageQualityReport",
  "session_id": "unknown",
  "timestamp": 1754363750.288685
}
单个工作流测试失败: 结果中缺少diagram_type字段
开始测试异步工作流执行
开始异步执行工作流: session_id=session_1754363750305
执行Supervisor节点: session_id=session_1754363750305
工作流状态更新: step=None
执行预处理节点: session_id=session_1754363750305
开始图像预处理: session_id=session_1754363750305
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=session_1754363750305
工作流状态更新: step=None
执行提取节点: session_id=session_1754363750305
开始语义提取: session_id=session_1754363750305
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=session_1754363750305
Agent extractor 失败且无法恢复
异步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
异步工作流执行完成，耗时: 0.07秒
执行结果: {
  "success": false,
  "error": "Type is not msgpack serializable: ImageQualityReport",
  "session_id": "unknown",
  "timestamp": 1754363750.376978
}
异步工作流测试失败: 结果中缺少diagram_type字段
开始测试多个工作流并发执行
LangGraph工作流初始化完成
执行第1个工作流
开始同步执行工作流: session_id=test_session_0
执行Supervisor节点: session_id=test_session_0
工作流状态更新: step=None
执行预处理节点: session_id=test_session_0
开始图像预处理: session_id=test_session_0
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_0
工作流状态更新: step=None
执行提取节点: session_id=test_session_0
开始语义提取: session_id=test_session_0
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_0
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
LangGraph工作流初始化完成
执行第2个工作流
开始同步执行工作流: session_id=test_session_1
执行Supervisor节点: session_id=test_session_1
工作流状态更新: step=None
执行预处理节点: session_id=test_session_1
开始图像预处理: session_id=test_session_1
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_1
工作流状态更新: step=None
执行提取节点: session_id=test_session_1
开始语义提取: session_id=test_session_1
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_1
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
LangGraph工作流初始化完成
执行第3个工作流
开始同步执行工作流: session_id=test_session_2
执行Supervisor节点: session_id=test_session_2
工作流状态更新: step=None
执行预处理节点: session_id=test_session_2
开始图像预处理: session_id=test_session_2
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_2
工作流状态更新: step=None
执行提取节点: session_id=test_session_2
开始语义提取: session_id=test_session_2
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=test_session_2
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
多个工作流执行完成，总耗时: 0.27秒
成功执行: 0/3 个工作流
所有工作流执行失败
开始测试错误处理
开始同步执行工作流: session_id=error_test_session
执行Supervisor节点: session_id=error_test_session
工作流状态更新: step=None
执行预处理节点: session_id=error_test_session
开始图像预处理: session_id=error_test_session
图像解码失败: Invalid base64-encoded string: number of data characters (17) cannot be 1 more than a multiple of 4
图像预处理失败: 图像解码失败
工作流状态更新: step=None
执行Supervisor节点: session_id=error_test_session
Agent preprocessor 失败且无法恢复
工作流状态更新: step=None
同步工作流执行失败: 工作流完成但未生成最终结果
错误处理测试完成，耗时: 0.03秒
错误处理结果: {
  "success": false,
  "error": "工作流完成但未生成最终结果",
  "session_id": "error_test_session",
  "timestamp": 1754363750.7382123
}
✓ 错误处理测试通过
开始测试性能指标
LangGraph工作流初始化完成
开始同步执行工作流: session_id=perf_test_0
执行Supervisor节点: session_id=perf_test_0
工作流状态更新: step=None
执行预处理节点: session_id=perf_test_0
开始图像预处理: session_id=perf_test_0
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_0
工作流状态更新: step=None
执行提取节点: session_id=perf_test_0
开始语义提取: session_id=perf_test_0
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_0
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
第1次执行耗时: 0.07秒
LangGraph工作流初始化完成
开始同步执行工作流: session_id=perf_test_1
执行Supervisor节点: session_id=perf_test_1
工作流状态更新: step=None
执行预处理节点: session_id=perf_test_1
开始图像预处理: session_id=perf_test_1
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_1
工作流状态更新: step=None
执行提取节点: session_id=perf_test_1
开始语义提取: session_id=perf_test_1
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_1
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
第2次执行耗时: 0.07秒
LangGraph工作流初始化完成
开始同步执行工作流: session_id=perf_test_2
执行Supervisor节点: session_id=perf_test_2
工作流状态更新: step=None
执行预处理节点: session_id=perf_test_2
开始图像预处理: session_id=perf_test_2
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_2
工作流状态更新: step=None
执行提取节点: session_id=perf_test_2
开始语义提取: session_id=perf_test_2
语义提取失败: 未找到分类结果
工作流状态更新: step=None
执行Supervisor节点: session_id=perf_test_2
Agent extractor 失败且无法恢复
同步工作流执行失败: Type is not msgpack serializable: ImageQualityReport
第3次执行耗时: 0.06秒
性能测试结果:
  平均执行时间: 0.07秒
  最短执行时间: 0.06秒
  最长执行时间: 0.07秒
  成功率: 0.00%
✓ 性能测试通过
==================================================
测试结果汇总:
  single_workflow: ✗ 失败
  async_workflow: ✗ 失败
  multiple_workflows: ✓ 通过 (0/3)
  error_handling: ✗ 失败
  performance: ✓ 通过
LangGraph多Agent系统测试完成
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理失败: 'dict' object has no attribute 'clarity_score'
图像预处理失败: 'dict' object has no attribute 'error'
预处理节点执行失败: 'dict' object has no attribute 'error'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
同步工作流执行失败: Type is not msgpack serializable: numpy.float64
工作流执行完成，耗时: 0.08秒
执行结果: {'success': False, 'error': 'Type is not msgpack serializable: numpy.float64', 'session_id': 'simple_test', 'timestamp': 1754364053.6999583}
工作流执行失败: Type is not msgpack serializable: numpy.float64
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理失败: 'dict' object has no attribute 'clarity_score'
图像预处理失败: 'dict' object has no attribute 'error'
预处理节点执行失败: 'dict' object has no attribute 'error'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
同步工作流执行失败: Type is not msgpack serializable: numpy.bool_
工作流执行完成，耗时: 0.06秒
执行结果: {'success': False, 'error': 'Type is not msgpack serializable: numpy.bool_', 'session_id': 'simple_test', 'timestamp': 1754364158.6044784}
工作流执行失败: Type is not msgpack serializable: numpy.bool_
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理失败: 'dict' object has no attribute 'clarity_score'
图像预处理失败: 'dict' object has no attribute 'error'
预处理节点执行失败: 'dict' object has no attribute 'error'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
同步工作流执行失败: 工作流完成但未生成最终结果
工作流执行完成，耗时: 0.08秒
执行结果: {'success': False, 'error': '工作流完成但未生成最终结果', 'session_id': 'simple_test', 'timestamp': 1754364190.4539058}
工作流执行失败: 工作流完成但未生成最终结果
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理失败: 'dict' object has no attribute 'error'
预处理节点执行失败: 'dict' object has no attribute 'error'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
同步工作流执行失败: 工作流完成但未生成最终结果
工作流执行完成，耗时: 0.06秒
执行结果: {'success': False, 'error': '工作流完成但未生成最终结果', 'session_id': 'simple_test', 'timestamp': 1754364227.6830406}
工作流执行失败: 工作流完成但未生成最终结果
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理失败: 'dict' object has no attribute 'error'
预处理节点执行失败: 'dict' object has no attribute 'error'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
同步工作流执行失败: 工作流完成但未生成最终结果
工作流执行完成，耗时: 0.07秒
执行结果: {'success': False, 'error': '工作流完成但未生成最终结果', 'session_id': 'simple_test', 'timestamp': 1754364287.8790195}
工作流执行失败: 工作流完成但未生成最终结果
开始基本工作流测试
LangGraph工作流初始化完成
开始同步执行工作流: session_id=simple_test
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行预处理节点: session_id=simple_test
开始图像预处理: session_id=simple_test
应用对比度增强
图像预处理成功: 耗时=0.02s
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
工作流状态更新: step=None
执行分类节点: session_id=simple_test
开始图形类型识别: session_id=simple_test
多模态模型分类失败: HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF0B2CCE10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
图形类型识别失败: 'dict' object has no attribute 'diagram_type'
工作流状态更新: step=None
执行Supervisor节点: session_id=simple_test
同步工作流执行失败: Type is not msgpack serializable: numpy.float64
工作流执行完成，耗时: 2.19秒
执行结果: {'success': False, 'error': 'Type is not msgpack serializable: numpy.float64', 'session_id': 'simple_test', 'timestamp': 1754364334.356742}
工作流执行失败: Type is not msgpack serializable: numpy.float64
