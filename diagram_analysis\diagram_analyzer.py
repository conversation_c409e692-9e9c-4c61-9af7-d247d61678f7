"""
图形分析主控制器

简化的图形语义分析流程，重点利用多模态模型能力
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, Union, Optional
import base64
from PIL import Image
import io

from utils.log_utils import setup_logger
from utils.config_manager import CONFIG
from .preprocess import DiagramPreprocessor
from .semantic_extractor import SemanticExtractor

logger = setup_logger("diagram_analyzer", CONFIG)


class DiagramAnalyzer:
    """图形分析器主类"""
    
    def __init__(self, config: dict = None):
        """
        初始化图形分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        
        # 初始化模块
        self.preprocessor = DiagramPreprocessor()
        self.semantic_extractor = SemanticExtractor()
        
        # 处理配置
        self.timeout = self.config.get('processing', {}).get('timeout', 120)
        self.enable_caching = self.config.get('processing', {}).get('enable_caching', True)
        
    def analyze_diagram(self, 
                       image: Union[str, np.ndarray, Image.Image],
                       output_format: str = 'mermaid',
                       diagram_type: str = 'auto',
                       enable_preprocessing: bool = True) -> Dict[str, Any]:
        """
        分析图形并提取语义信息
        
        Args:
            image: 输入图像（base64字符串、numpy数组或PIL图像）
            output_format: 输出格式 ('mermaid', 'json', 'text', 'all')
            diagram_type: 图形类型，'auto'表示自动识别
            enable_preprocessing: 是否启用预处理
            
        Returns:
            dict: 分析结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始图形分析: 输出格式={output_format}, 图形类型={diagram_type}")
            
            # 加载和预处理图像
            processed_image = self._load_and_preprocess_image(image, enable_preprocessing)
            if processed_image is None:
                raise ValueError("图像加载或预处理失败")
            
            # 语义提取
            semantic_results = self.semantic_extractor.extract_semantics(processed_image, diagram_type)
            
            # 根据输出格式筛选结果
            filtered_results = self._filter_results_by_format(semantic_results, output_format)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 构建最终结果
            final_result = {
                'success': True,
                'diagram_type': semantic_results['diagram_type'],
                'processing_time': processing_time,
                'results': filtered_results,
                'metadata': {
                    'output_format': output_format,
                    'preprocessing_enabled': enable_preprocessing,
                    'image_info': {
                        'shape': processed_image.shape,
                        'dtype': str(processed_image.dtype)
                    }
                }
            }
            
            logger.info(f"图形分析完成: 类型={semantic_results['diagram_type']}, 耗时={processing_time:.2f}s")
            return final_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"图形分析失败: {str(e)}")
            return self._create_error_result(str(e), processing_time, output_format)
    
    def analyze_to_mermaid(self, image: Union[str, np.ndarray, Image.Image], diagram_type: str = 'auto') -> Dict[str, Any]:
        """
        将图形转换为Mermaid格式（便捷方法）
        
        Args:
            image: 输入图像
            diagram_type: 图形类型
            
        Returns:
            dict: Mermaid转换结果
        """
        result = self.analyze_diagram(image, output_format='mermaid', diagram_type=diagram_type)
        
        if result['success']:
            mermaid_data = result['results'].get('mermaid', {})
            return {
                'success': mermaid_data.get('success', False),
                'mermaid_code': mermaid_data.get('code', ''),
                'is_valid': mermaid_data.get('is_valid', False),
                'diagram_type': result['diagram_type'],
                'processing_time': result['processing_time']
            }
        else:
            return {
                'success': False,
                'mermaid_code': '',
                'is_valid': False,
                'diagram_type': 'unknown',
                'processing_time': result['processing_time'],
                'error': result.get('error', '')
            }
    
    def batch_analyze(self, images: list, output_format: str = 'mermaid', diagram_type: str = 'auto') -> list:
        """
        批量分析图形
        
        Args:
            images: 图像列表
            output_format: 输出格式
            diagram_type: 图形类型
            
        Returns:
            list: 分析结果列表
        """
        logger.info(f"开始批量图形分析: {len(images)}张图片")
        
        results = []
        for i, image in enumerate(images):
            try:
                result = self.analyze_diagram(image, output_format, diagram_type)
                result['batch_index'] = i
                results.append(result)
                logger.debug(f"第{i+1}张图片分析完成")
            except Exception as e:
                logger.error(f"第{i+1}张图片分析失败: {str(e)}")
                error_result = self._create_error_result(str(e), 0.0, output_format)
                error_result['batch_index'] = i
                results.append(error_result)
        
        logger.info(f"批量图形分析完成: {len(results)}个结果")
        return results
    
    def _load_and_preprocess_image(self, image: Union[str, np.ndarray, Image.Image], enable_preprocessing: bool) -> Optional[np.ndarray]:
        """加载和预处理图像"""
        try:
            # 加载图像
            if isinstance(image, str):
                # base64字符串
                if image.startswith('data:image'):
                    image = image.split(',')[1]
                image_data = base64.b64decode(image)
                pil_image = Image.open(io.BytesIO(image_data))
                cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            elif isinstance(image, Image.Image):
                # PIL图像
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            elif isinstance(image, np.ndarray):
                # numpy数组
                cv_image = image.copy()
            else:
                return None
            
            # 预处理（如果启用）
            if enable_preprocessing:
                # 简化的预处理：只做基本的尺寸调整和降噪
                processed_image = self._simple_preprocess(cv_image)
            else:
                processed_image = cv_image
            
            return processed_image
            
        except Exception as e:
            logger.error(f"图像加载预处理失败: {str(e)}")
            return None
    
    def _simple_preprocess(self, image: np.ndarray) -> np.ndarray:
        """简化的预处理"""
        try:
            # 尺寸调整
            h, w = image.shape[:2]
            max_size = self.config.get('processing', {}).get('max_image_size', [2048, 2048])
            
            if h > max_size[1] or w > max_size[0]:
                scale = min(max_size[1] / h, max_size[0] / w)
                new_h, new_w = int(h * scale), int(w * scale)
                image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
            
            # 轻微降噪
            denoised = cv2.GaussianBlur(image, (3, 3), 0)
            
            # 对比度增强
            if len(denoised.shape) == 3:
                gray = cv2.cvtColor(denoised, cv2.COLOR_BGR2GRAY)
            else:
                gray = denoised.copy()
            
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 转回彩色
            if len(denoised.shape) == 3:
                result = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
            else:
                result = enhanced
            
            return result
            
        except Exception as e:
            logger.warning(f"简化预处理失败: {str(e)}")
            return image
    
    def _filter_results_by_format(self, semantic_results: Dict[str, Any], output_format: str) -> Dict[str, Any]:
        """根据输出格式筛选结果"""
        if output_format == 'all':
            return {
                'mermaid': semantic_results.get('mermaid', {}),
                'structured_json': semantic_results.get('structured_json', {}),
                'text_description': semantic_results.get('text_description', {})
            }
        elif output_format == 'mermaid':
            return {
                'mermaid': semantic_results.get('mermaid', {})
            }
        elif output_format == 'json':
            return {
                'structured_json': semantic_results.get('structured_json', {})
            }
        elif output_format == 'text':
            return {
                'text_description': semantic_results.get('text_description', {})
            }
        else:
            # 默认返回Mermaid
            return {
                'mermaid': semantic_results.get('mermaid', {})
            }
    
    def _create_error_result(self, error_message: str, processing_time: float, output_format: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'diagram_type': 'unknown',
            'processing_time': processing_time,
            'results': {},
            'metadata': {
                'output_format': output_format,
                'preprocessing_enabled': False,
                'image_info': {}
            },
            'error': error_message
        }


# 便捷函数
def analyze_diagram_to_mermaid(image: Union[str, np.ndarray, Image.Image], 
                              diagram_type: str = 'auto',
                              config: dict = None) -> Dict[str, Any]:
    """
    将图形转换为Mermaid格式（便捷函数）
    
    Args:
        image: 输入图像
        diagram_type: 图形类型
        config: 配置字典
        
    Returns:
        dict: Mermaid转换结果
    """
    analyzer = DiagramAnalyzer(config)
    return analyzer.analyze_to_mermaid(image, diagram_type)


def analyze_diagram_full(image: Union[str, np.ndarray, Image.Image],
                        output_format: str = 'all',
                        diagram_type: str = 'auto',
                        config: dict = None) -> Dict[str, Any]:
    """
    完整的图形分析（便捷函数）
    
    Args:
        image: 输入图像
        output_format: 输出格式
        diagram_type: 图形类型
        config: 配置字典
        
    Returns:
        dict: 完整分析结果
    """
    analyzer = DiagramAnalyzer(config)
    return analyzer.analyze_diagram(image, output_format, diagram_type)
