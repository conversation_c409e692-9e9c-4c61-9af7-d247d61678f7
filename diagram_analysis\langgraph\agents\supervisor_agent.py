"""
Supervisor协调器Agent

负责协调和管理整个图形分析工作流的Supervisor Agent
"""

import time
from typing import Dict, Any, List, Optional, Literal
from langchain_core.messages import HumanMessage, AIMessage

from ..state_management import (
    DiagramAnalysisState, AgentReport, AgentType, ProcessingStep,
    state_manager
)
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("supervisor_agent", CONFIG)


class SupervisorAgent:
    """Supervisor协调器Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化Supervisor Agent
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        self.agent_type = AgentType.SUPERVISOR
        
        # 工作流配置
        self.workflow_config = self.config.get('workflow', {})
        self.max_retries = self.workflow_config.get('max_retries', 3)
        self.timeout_seconds = self.workflow_config.get('timeout_seconds', 300)
        
        # Agent执行顺序
        self.agent_sequence = [
            'preprocessor',
            'classifier', 
            'extractor',
            'integrator'
        ]
        
        # 错误处理策略
        self.error_strategies = {
            'preprocessor': 'retry',  # 预处理失败重试
            'classifier': 'fallback',  # 分类失败使用默认类型
            'extractor': 'retry',  # 提取失败重试
            'integrator': 'retry'  # 集成失败重试
        }
    
    def coordinate_workflow(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """
        协调整个工作流
        
        Args:
            state: 当前状态
            
        Returns:
            DiagramAnalysisState: 最终状态
        """
        start_time = time.time()
        
        try:
            logger.info(f"Supervisor开始协调工作流: session_id={state['session_id']}")
            
            # 初始化Supervisor报告
            self._add_supervisor_report(state, "工作流开始", success=True)
            
            # 检查输入有效性
            if not self._validate_input(state):
                return state_manager.set_error(state, "输入验证失败", self.agent_type)
            
            # 设置初始的下一个Agent
            state["next_agent"] = self.agent_sequence[0]
            
            # 执行工作流
            current_step = 0
            max_steps = len(self.agent_sequence) * (self.max_retries + 1)  # 防止无限循环
            
            while current_step < max_steps:
                # 检查超时
                if time.time() - start_time > self.timeout_seconds:
                    return state_manager.set_error(state, "工作流执行超时", self.agent_type)
                
                # 检查是否完成
                if state.get("current_step") == ProcessingStep.COMPLETED:
                    break
                
                # 检查是否失败
                if state.get("current_step") == ProcessingStep.FAILED:
                    break
                
                # 获取下一个要执行的Agent
                next_agent = state.get("next_agent")
                if not next_agent:
                    break
                
                # 执行决策
                decision = self._make_decision(state, next_agent)
                
                if decision == "CONTINUE":
                    # 继续执行下一个Agent
                    state["next_agent"] = self._get_next_agent(next_agent)
                elif decision == "RETRY":
                    # 重试当前Agent
                    state["next_agent"] = next_agent
                elif decision == "SKIP":
                    # 跳过当前Agent
                    state["next_agent"] = self._get_next_agent(next_agent)
                elif decision == "COMPLETE":
                    # 工作流完成
                    break
                elif decision == "FAIL":
                    # 工作流失败
                    return state_manager.set_error(state, f"Agent {next_agent} 执行失败", self.agent_type)
                
                current_step += 1
            
            # 检查最终状态
            if state.get("current_step") != ProcessingStep.COMPLETED:
                if current_step >= max_steps:
                    return state_manager.set_error(state, "工作流执行步骤超限", self.agent_type)
                else:
                    return state_manager.set_error(state, "工作流未正常完成", self.agent_type)
            
            # 工作流成功完成
            total_time = time.time() - start_time
            self._add_supervisor_report(
                state, 
                f"工作流成功完成，总耗时: {total_time:.2f}秒", 
                success=True,
                data={"total_time": total_time, "steps_executed": current_step}
            )
            
            logger.info(f"Supervisor工作流协调完成: 耗时={total_time:.2f}s, 步骤={current_step}")
            return state
            
        except Exception as e:
            error_msg = f"Supervisor协调失败: {str(e)}"
            logger.error(error_msg)
            self._add_supervisor_report(state, error_msg, success=False, error=str(e))
            return state_manager.set_error(state, error_msg, self.agent_type)
    
    def _make_decision(self, state: DiagramAnalysisState, current_agent: str) -> Literal["CONTINUE", "RETRY", "SKIP", "COMPLETE", "FAIL"]:
        """
        做出执行决策
        
        Args:
            state: 当前状态
            current_agent: 当前Agent
            
        Returns:
            str: 决策结果
        """
        try:
            # 获取当前Agent的最新报告
            agent_reports = state.get("agent_reports", [])
            current_agent_reports = [
                report for report in agent_reports 
                if report.agent_type.value == current_agent
            ]
            
            # 如果没有执行过，继续执行
            if not current_agent_reports:
                return "CONTINUE"
            
            # 获取最新报告
            latest_report = current_agent_reports[-1]
            
            # 如果最新执行成功
            if latest_report.success:
                # 检查是否是最后一个Agent
                if current_agent == self.agent_sequence[-1]:
                    return "COMPLETE"
                else:
                    return "CONTINUE"
            
            # 如果执行失败，检查重试策略
            retry_count = len(current_agent_reports)
            strategy = self.error_strategies.get(current_agent, 'retry')
            
            if strategy == 'retry' and retry_count <= self.max_retries:
                logger.info(f"Agent {current_agent} 失败，执行重试 ({retry_count}/{self.max_retries})")
                return "RETRY"
            elif strategy == 'fallback':
                logger.info(f"Agent {current_agent} 失败，使用fallback策略")
                # 对于分类器，可以使用默认类型继续
                if current_agent == 'classifier':
                    self._apply_fallback_classification(state)
                    return "CONTINUE"
                else:
                    return "SKIP"
            else:
                logger.error(f"Agent {current_agent} 失败且无法恢复")
                return "FAIL"
                
        except Exception as e:
            logger.error(f"决策制定失败: {str(e)}")
            return "FAIL"
    
    def _validate_input(self, state: DiagramAnalysisState) -> bool:
        """验证输入有效性"""
        try:
            # 检查必需字段
            if not state.get("original_image"):
                logger.error("缺少原始图像")
                return False
            
            if not state.get("request_params"):
                logger.error("缺少请求参数")
                return False
            
            # 检查图像格式
            original_image = state["original_image"]
            if not isinstance(original_image, str) or len(original_image) < 100:
                logger.error("图像格式无效")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"输入验证失败: {str(e)}")
            return False
    
    def _get_next_agent(self, current_agent: str) -> Optional[str]:
        """获取下一个Agent"""
        try:
            current_index = self.agent_sequence.index(current_agent)
            if current_index < len(self.agent_sequence) - 1:
                return self.agent_sequence[current_index + 1]
            else:
                return None  # 已经是最后一个Agent
        except ValueError:
            logger.error(f"未知的Agent: {current_agent}")
            return None
    
    def _apply_fallback_classification(self, state: DiagramAnalysisState):
        """应用分类fallback策略"""
        try:
            from ..state_management import TypeClassificationResult
            
            # 使用默认分类结果
            fallback_result = TypeClassificationResult(
                diagram_type='flowchart',
                confidence=0.5,
                alternative_types=[],
                reasoning='分类失败，使用默认类型',
                features_detected={},
                validation_passed=False
            )
            
            state = state_manager.set_classification_result(state, fallback_result)
            
            logger.info("应用分类fallback策略: 使用默认flowchart类型")
            
        except Exception as e:
            logger.error(f"Fallback策略应用失败: {str(e)}")
    
    def _add_supervisor_report(self, 
                             state: DiagramAnalysisState, 
                             message: str, 
                             success: bool = True,
                             data: Dict[str, Any] = None,
                             error: str = None):
        """添加Supervisor报告"""
        try:
            report = AgentReport(
                agent_type=self.agent_type,
                step=state.get("current_step", ProcessingStep.INITIALIZED),
                success=success,
                processing_time=0.0,
                message=message,
                data=data or {},
                error=error
            )
            
            state = state_manager.add_agent_report(state, report)
            
            # 添加消息
            if success:
                msg = AIMessage(content=f"[Supervisor] {message}")
            else:
                msg = AIMessage(content=f"[Supervisor Error] {message}")
            
            state["messages"].append(msg)
            
        except Exception as e:
            logger.error(f"添加Supervisor报告失败: {str(e)}")
    
    def get_workflow_status(self, state: DiagramAnalysisState) -> Dict[str, Any]:
        """获取工作流状态"""
        try:
            agent_reports = state.get("agent_reports", [])
            
            # 统计各Agent执行情况
            agent_status = {}
            for agent_name in self.agent_sequence:
                agent_reports_filtered = [
                    report for report in agent_reports 
                    if report.agent_type.value == agent_name
                ]
                
                if agent_reports_filtered:
                    latest_report = agent_reports_filtered[-1]
                    agent_status[agent_name] = {
                        'executed': True,
                        'success': latest_report.success,
                        'attempts': len(agent_reports_filtered),
                        'last_message': latest_report.message
                    }
                else:
                    agent_status[agent_name] = {
                        'executed': False,
                        'success': False,
                        'attempts': 0,
                        'last_message': 'Not executed'
                    }
            
            return {
                'current_step': state.get("current_step", ProcessingStep.INITIALIZED).value,
                'next_agent': state.get("next_agent"),
                'total_processing_time': state.get("total_processing_time", 0.0),
                'success': state.get("success", False),
                'agent_status': agent_status,
                'error_count': len(state.get("error_messages", [])),
                'has_final_result': state.get("final_result") is not None
            }
            
        except Exception as e:
            logger.error(f"获取工作流状态失败: {str(e)}")
            return {'error': str(e)}
    
    def estimate_remaining_time(self, state: DiagramAnalysisState) -> float:
        """估算剩余处理时间"""
        try:
            agent_reports = state.get("agent_reports", [])
            current_step = state.get("current_step", ProcessingStep.INITIALIZED)
            
            # 计算已完成Agent的平均时间
            completed_agents = set()
            total_time = 0.0
            
            for report in agent_reports:
                if report.success and report.agent_type.value not in completed_agents:
                    completed_agents.add(report.agent_type.value)
                    total_time += report.processing_time
            
            if completed_agents:
                avg_time_per_agent = total_time / len(completed_agents)
            else:
                avg_time_per_agent = 10.0  # 默认估算
            
            # 计算剩余Agent数量
            remaining_agents = len(self.agent_sequence) - len(completed_agents)
            
            return remaining_agents * avg_time_per_agent
            
        except Exception as e:
            logger.error(f"时间估算失败: {str(e)}")
            return 30.0  # 默认估算


def create_supervisor_agent(config: Dict[str, Any] = None) -> SupervisorAgent:
    """创建Supervisor Agent"""
    return SupervisorAgent(config)
