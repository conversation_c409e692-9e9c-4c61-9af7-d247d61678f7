"""
图形类型识别模块

使用多模态模型识别软件领域图形类型
"""

import cv2
import numpy as np
import base64
import io
from typing import Dict, Any, List, Tuple, Optional
from PIL import Image
import requests
import json
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("diagram_classifier", CONFIG)


class DiagramClassifier:
    """图形类型分类器"""
    
    def __init__(self, config: dict = None):
        """
        初始化图形分类器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        
        # 多模态模型配置
        self.vision_config = self.config.get('vision_model', {})
        self.base_url = self.vision_config.get('base_url', '')
        self.api_key = self.vision_config.get('api_key', '')
        self.model_name = self.vision_config.get('model_name', 'Qwen2-VL-7B')
        
        # 支持的图形类型
        self.supported_types = self.config.get('supported_types', [
            'sequence_diagram', 'flowchart', 'class_diagram', 'use_case_diagram',
            'activity_diagram', 'state_diagram', 'component_diagram', 'deployment_diagram'
        ])
        
        # 分类提示词模板
        self.classification_prompt = self._build_classification_prompt()
        
        # 特征匹配规则
        self.feature_rules = self._build_feature_rules()
    
    def classify_diagram(self, image: np.ndarray, preprocess_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        识别图形类型
        
        Args:
            image: 输入图像
            preprocess_results: 预处理结果
            
        Returns:
            dict: 分类结果
        """
        try:
            logger.info("开始图形类型识别")
            
            # 多模态模型分类
            vision_result = self._classify_with_vision_model(image)
            
            # 特征匹配验证
            feature_result = self._classify_with_features(preprocess_results) if preprocess_results else {}
            
            # 融合分类结果
            final_result = self._fuse_classification_results(vision_result, feature_result)
            
            logger.info(f"图形类型识别完成: {final_result['diagram_type']} (置信度: {final_result['confidence']:.3f})")
            return final_result
            
        except Exception as e:
            logger.error(f"图形类型识别失败: {str(e)}")
            return {
                'diagram_type': 'unknown',
                'confidence': 0.0,
                'vision_result': {},
                'feature_result': {},
                'error': str(e)
            }
    
    def _classify_with_vision_model(self, image: np.ndarray) -> Dict[str, Any]:
        """使用多模态模型进行分类"""
        try:
            # 将图像转换为base64
            image_base64 = self._image_to_base64(image)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        },
                        {
                            "type": "text",
                            "text": self.classification_prompt
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 1024,
                "temperature": 0.1
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 解析结果
                return self._parse_vision_result(content)
            else:
                logger.error(f"多模态模型请求失败: {response.status_code}")
                return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': ''}
                
        except Exception as e:
            logger.error(f"多模态模型分类失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': '', 'error': str(e)}
    
    def _classify_with_features(self, preprocess_results: Dict[str, Any]) -> Dict[str, Any]:
        """基于特征进行分类验证"""
        try:
            shapes = preprocess_results.get('shapes', [])
            lines = preprocess_results.get('lines', [])
            text_regions = preprocess_results.get('text_regions', [])
            
            # 计算特征统计
            features = {
                'shape_count': len(shapes),
                'line_count': len(lines),
                'text_count': len(text_regions),
                'rectangle_count': len([s for s in shapes if s['type'] in ['rectangle', 'square']]),
                'circle_count': len([s for s in shapes if s['type'] in ['circle', 'ellipse']]),
                'triangle_count': len([s for s in shapes if s['type'] == 'triangle']),
                'horizontal_lines': len([l for l in lines if abs(l['angle']) < 15 or abs(l['angle']) > 165]),
                'vertical_lines': len([l for l in lines if 75 < abs(l['angle']) < 105]),
                'diagonal_lines': len([l for l in lines if 15 <= abs(l['angle']) <= 75 or 105 <= abs(l['angle']) <= 165])
            }
            
            # 应用分类规则
            scores = {}
            for diagram_type, rules in self.feature_rules.items():
                score = self._calculate_rule_score(features, rules)
                scores[diagram_type] = score
            
            # 选择最高分的类型
            best_type = max(scores, key=scores.get) if scores else 'unknown'
            best_score = scores.get(best_type, 0.0)
            
            return {
                'diagram_type': best_type,
                'confidence': best_score,
                'features': features,
                'scores': scores
            }
            
        except Exception as e:
            logger.error(f"特征分类失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'error': str(e)}
    
    def _fuse_classification_results(self, vision_result: Dict[str, Any], feature_result: Dict[str, Any]) -> Dict[str, Any]:
        """融合分类结果"""
        vision_type = vision_result.get('diagram_type', 'unknown')
        vision_conf = vision_result.get('confidence', 0.0)
        
        feature_type = feature_result.get('diagram_type', 'unknown')
        feature_conf = feature_result.get('confidence', 0.0)
        
        # 权重配置
        vision_weight = 0.7
        feature_weight = 0.3
        
        # 如果两种方法结果一致，提高置信度
        if vision_type == feature_type and vision_type != 'unknown':
            final_type = vision_type
            final_conf = min(1.0, vision_conf * vision_weight + feature_conf * feature_weight + 0.2)
        else:
            # 选择置信度更高的结果
            if vision_conf * vision_weight >= feature_conf * feature_weight:
                final_type = vision_type
                final_conf = vision_conf * vision_weight
            else:
                final_type = feature_type
                final_conf = feature_conf * feature_weight
        
        return {
            'diagram_type': final_type,
            'confidence': final_conf,
            'vision_result': vision_result,
            'feature_result': feature_result,
            'fusion_method': 'weighted_average'
        }
    
    def _build_classification_prompt(self) -> str:
        """构建分类提示词"""
        types_desc = {
            'sequence_diagram': '时序图 - 显示对象间的交互时序，包含生命线、激活框、消息箭头',
            'flowchart': '流程图 - 显示业务流程，包含开始/结束节点、处理节点、决策菱形、连接箭头',
            'class_diagram': '类图 - 显示类的结构和关系，包含类框、属性、方法、继承/关联线',
            'use_case_diagram': '用例图 - 显示系统功能和用户交互，包含用例椭圆、角色、系统边界',
            'activity_diagram': '活动图 - 显示活动流程，包含活动节点、决策点、并发分支',
            'state_diagram': '状态图 - 显示状态转换，包含状态节点、转换箭头、触发条件',
            'component_diagram': '组件图 - 显示组件结构，包含组件框、接口、依赖关系',
            'deployment_diagram': '部署图 - 显示系统部署，包含节点、组件、通信路径'
        }
        
        prompt = """请分析这张软件工程图形，识别其类型。

支持的图形类型：
"""
        for type_key, desc in types_desc.items():
            prompt += f"- {desc}\n"
        
        prompt += """
请按以下JSON格式返回结果：
{
    "diagram_type": "图形类型（使用英文标识符）",
    "confidence": 置信度（0-1之间的数值）,
    "reasoning": "识别理由（简要说明识别依据）"
}

注意：
1. 仔细观察图形中的元素类型、布局特征、连接关系
2. 置信度应该反映识别的确定程度
3. 如果无法确定类型，返回"unknown"
"""
        return prompt
    
    def _build_feature_rules(self) -> Dict[str, Dict[str, Any]]:
        """构建特征匹配规则"""
        return {
            'sequence_diagram': {
                'vertical_lines': {'min': 2, 'weight': 0.3},  # 生命线
                'horizontal_lines': {'min': 1, 'weight': 0.2}, # 消息线
                'rectangle_count': {'min': 2, 'weight': 0.3},  # 对象框
                'text_count': {'min': 3, 'weight': 0.2}        # 文本标签
            },
            'flowchart': {
                'rectangle_count': {'min': 2, 'weight': 0.3},  # 处理框
                'circle_count': {'min': 1, 'weight': 0.2},     # 开始/结束
                'triangle_count': {'min': 0, 'weight': 0.1},   # 决策点
                'line_count': {'min': 2, 'weight': 0.4}        # 连接线
            },
            'class_diagram': {
                'rectangle_count': {'min': 2, 'weight': 0.4},  # 类框
                'horizontal_lines': {'min': 2, 'weight': 0.2}, # 分隔线
                'diagonal_lines': {'min': 1, 'weight': 0.2},   # 关系线
                'text_count': {'min': 4, 'weight': 0.2}        # 类名、属性、方法
            },
            'use_case_diagram': {
                'circle_count': {'min': 2, 'weight': 0.4},     # 用例椭圆
                'rectangle_count': {'min': 1, 'weight': 0.2},  # 系统边界
                'line_count': {'min': 2, 'weight': 0.3},       # 关联线
                'text_count': {'min': 3, 'weight': 0.1}        # 标签
            }
        }
    
    def _calculate_rule_score(self, features: Dict[str, int], rules: Dict[str, Dict[str, Any]]) -> float:
        """计算规则匹配分数"""
        total_score = 0.0
        total_weight = 0.0
        
        for feature_name, rule in rules.items():
            if feature_name in features:
                feature_value = features[feature_name]
                min_value = rule.get('min', 0)
                weight = rule.get('weight', 1.0)
                
                # 计算匹配分数
                if feature_value >= min_value:
                    score = min(1.0, feature_value / max(min_value * 2, 1))
                else:
                    score = feature_value / max(min_value, 1)
                
                total_score += score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _parse_vision_result(self, content: str) -> Dict[str, Any]:
        """解析多模态模型结果"""
        try:
            # 尝试解析JSON
            if '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                json_str = content[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证结果格式
                diagram_type = result.get('diagram_type', 'unknown')
                confidence = float(result.get('confidence', 0.0))
                reasoning = result.get('reasoning', '')
                
                return {
                    'diagram_type': diagram_type,
                    'confidence': confidence,
                    'reasoning': reasoning
                }
            else:
                # 如果不是JSON格式，尝试从文本中提取信息
                return self._extract_from_text(content)
                
        except Exception as e:
            logger.warning(f"解析多模态模型结果失败: {str(e)}")
            return {'diagram_type': 'unknown', 'confidence': 0.0, 'reasoning': content}
    
    def _extract_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取分类信息"""
        text_lower = text.lower()
        
        # 简单的关键词匹配
        type_keywords = {
            'sequence_diagram': ['sequence', 'time', '时序', '交互'],
            'flowchart': ['flow', 'process', '流程', '业务'],
            'class_diagram': ['class', 'uml', '类图', '类'],
            'use_case_diagram': ['use case', 'actor', '用例', '角色'],
            'activity_diagram': ['activity', '活动', '流程'],
            'state_diagram': ['state', '状态', '转换'],
            'component_diagram': ['component', '组件', '模块'],
            'deployment_diagram': ['deployment', '部署', '节点']
        }
        
        best_type = 'unknown'
        best_score = 0
        
        for diagram_type, keywords in type_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > best_score:
                best_score = score
                best_type = diagram_type
        
        confidence = min(0.8, best_score * 0.2) if best_score > 0 else 0.0
        
        return {
            'diagram_type': best_type,
            'confidence': confidence,
            'reasoning': f'基于关键词匹配: {best_score}个匹配'
        }
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """将图像转换为base64字符串"""
        # 转换为RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        
        # 转换为base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
