"""
相似度比对主流程控制模块

整合各个相似度计算模块，实现完整的图片相似度比对流程
"""

import time
import numpy as np
from typing import Dict, Any, Union, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import base64
from PIL import Image
import io

from utils.log_utils import setup_logger
from utils.config_manager import CONFIG
from similarity.preprocess import ImagePreprocessor
from similarity.pixel_similarity import PixelSimilarityCalculator
from similarity.hash_similarity import HashSimilarityCalculator
from similarity.feature_similarity import FeatureSimilarityCalculator
from similarity.ocr_similarity import OCRSimilarityCalculator
from similarity.fusion import MultiModalScorer

logger = setup_logger("similarity_main", CONFIG)


class SimilarityComparator:
    """相似度比对器主类"""
    
    def __init__(self, config: dict = None):
        """
        初始化相似度比对器
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('similarity', {})
        
        # 初始化各个模块
        self.preprocessor = ImagePreprocessor()
        self.pixel_calculator = PixelSimilarityCalculator()
        self.hash_calculator = HashSimilarityCalculator()
        self.feature_calculator = FeatureSimilarityCalculator()
        self.ocr_calculator = OCRSimilarityCalculator()
        self.fusion_scorer = MultiModalScorer()
        
        # 并发配置
        self.max_workers = self.config.get('max_workers', 4)
        self.enable_parallel = self.config.get('enable_parallel', True)
        
        # 快速筛选配置
        self.enable_fast_screening = self.config.get('enable_fast_screening', True)
        self.fast_screening_threshold = self.config.get('fast_screening_threshold', 0.3)
        
    def compare_two_answers(self, 
                          image1: Union[str, np.ndarray, Image.Image],
                          image2: Union[str, np.ndarray, Image.Image],
                          subject: str = 'general',
                          question_type: str = 'general',
                          enable_fast_screening: bool = None) -> Dict[str, Any]:
        """
        比较两张答题图片的相似度
        
        Args:
            image1: 第一张图片（base64字符串、numpy数组或PIL图像）
            image2: 第二张图片（base64字符串、numpy数组或PIL图像）
            subject: 学科类型
            question_type: 题型
            enable_fast_screening: 是否启用快速筛选
            
        Returns:
            dict: 相似度比对结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始相似度比对: 学科={subject}, 题型={question_type}")
            
            # 图像预处理
            processed_image1 = self.preprocessor.preprocess_image(image1)
            processed_image2 = self.preprocessor.preprocess_image(image2)
            
            if processed_image1 is None or processed_image2 is None:
                raise ValueError("图像预处理失败")
            
            # 快速筛选（可选）
            if enable_fast_screening is None:
                enable_fast_screening = self.enable_fast_screening
            
            if enable_fast_screening:
                is_similar = self.hash_calculator.is_similar_fast(
                    processed_image1, processed_image2, 
                    threshold=self.fast_screening_threshold
                )
                if not is_similar:
                    logger.info("快速筛选：图片差异较大，跳过详细计算")
                    return self._create_low_similarity_result(start_time, subject, question_type)
            
            # 并行计算各模态相似度
            if self.enable_parallel:
                results = self._calculate_similarity_parallel(
                    processed_image1, processed_image2
                )
            else:
                results = self._calculate_similarity_sequential(
                    processed_image1, processed_image2
                )
            
            # 多模态融合评分
            fusion_result = self.fusion_scorer.calculate_fusion_score(
                pixel_results=results.get('pixel'),
                hash_results=results.get('hash'),
                feature_results=results.get('feature'),
                ocr_results=results.get('ocr'),
                subject=subject,
                question_type=question_type
            )
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            # 构建最终结果
            final_result = {
                'similarity_score': fusion_result['fusion_score'],
                'confidence': fusion_result['overall_confidence'],
                'risk_level': fusion_result['risk_level'],
                'recommendation': fusion_result['recommendation'],
                'processing_time': total_time,
                'subject': subject,
                'question_type': question_type,
                'detailed_results': {
                    'pixel': results.get('pixel', {}),
                    'hash': results.get('hash', {}),
                    'feature': results.get('feature', {}),
                    'ocr': results.get('ocr', {}),
                    'fusion': fusion_result
                },
                'metadata': {
                    'fast_screening_used': enable_fast_screening,
                    'parallel_processing': self.enable_parallel,
                    'image_shapes': {
                        'original': (processed_image1.shape, processed_image2.shape),
                        'processed': (processed_image1.shape, processed_image2.shape)
                    }
                }
            }
            
            logger.info(f"相似度比对完成: 分数={fusion_result['fusion_score']:.4f}, 耗时={total_time:.2f}s")
            return final_result
            
        except Exception as e:
            logger.error(f"相似度比对失败: {str(e)}")
            return self._create_error_result(str(e), time.time() - start_time)
    
    def _calculate_similarity_parallel(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        并行计算各模态相似度
        
        Args:
            image1: 第一张预处理后的图像
            image2: 第二张预处理后的图像
            
        Returns:
            dict: 各模态相似度结果
        """
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_modality = {
                executor.submit(self.pixel_calculator.calculate_similarity, image1, image2): 'pixel',
                executor.submit(self.hash_calculator.calculate_similarity, image1, image2): 'hash',
                executor.submit(self.feature_calculator.calculate_similarity, image1, image2): 'feature',
                executor.submit(self.ocr_calculator.calculate_multi_method_similarity, image1, image2): 'ocr'
            }
            
            # 收集结果
            for future in as_completed(future_to_modality):
                modality = future_to_modality[future]
                try:
                    result = future.result()
                    results[modality] = result
                    logger.debug(f"{modality}模态计算完成")
                except Exception as e:
                    logger.warning(f"{modality}模态计算失败: {str(e)}")
                    results[modality] = {}
        
        return results
    
    def _calculate_similarity_sequential(self, image1: np.ndarray, image2: np.ndarray) -> Dict[str, Any]:
        """
        顺序计算各模态相似度
        
        Args:
            image1: 第一张预处理后的图像
            image2: 第二张预处理后的图像
            
        Returns:
            dict: 各模态相似度结果
        """
        results = {}
        
        try:
            results['pixel'] = self.pixel_calculator.calculate_similarity(image1, image2)
            logger.debug("像素级相似度计算完成")
        except Exception as e:
            logger.warning(f"像素级相似度计算失败: {str(e)}")
            results['pixel'] = {}
        
        try:
            results['hash'] = self.hash_calculator.calculate_similarity(image1, image2)
            logger.debug("哈希相似度计算完成")
        except Exception as e:
            logger.warning(f"哈希相似度计算失败: {str(e)}")
            results['hash'] = {}
        
        try:
            results['feature'] = self.feature_calculator.calculate_similarity(image1, image2)
            logger.debug("特征匹配相似度计算完成")
        except Exception as e:
            logger.warning(f"特征匹配相似度计算失败: {str(e)}")
            results['feature'] = {}
        
        try:
            results['ocr'] = self.ocr_calculator.calculate_multi_method_similarity(image1, image2)
            logger.debug("OCR文本相似度计算完成")
        except Exception as e:
            logger.warning(f"OCR文本相似度计算失败: {str(e)}")
            results['ocr'] = {}
        
        return results
    
    def _create_low_similarity_result(self, start_time: float, subject: str, question_type: str) -> Dict[str, Any]:
        """
        创建低相似度结果（快速筛选）
        
        Args:
            start_time: 开始时间
            subject: 学科
            question_type: 题型
            
        Returns:
            dict: 低相似度结果
        """
        total_time = time.time() - start_time
        
        return {
            'similarity_score': 0.0,
            'confidence': 0.8,  # 快速筛选的置信度较高
            'risk_level': 'low',
            'recommendation': "快速筛选：相似度较低，可能不存在抄袭",
            'processing_time': total_time,
            'subject': subject,
            'question_type': question_type,
            'detailed_results': {
                'pixel': {},
                'hash': {'fast_screening': True, 'similarity': 0.0},
                'feature': {},
                'ocr': {},
                'fusion': {}
            },
            'metadata': {
                'fast_screening_used': True,
                'parallel_processing': False,
                'early_termination': True
            }
        }
    
    def _create_error_result(self, error_message: str, processing_time: float) -> Dict[str, Any]:
        """
        创建错误结果
        
        Args:
            error_message: 错误信息
            processing_time: 处理时间
            
        Returns:
            dict: 错误结果
        """
        return {
            'similarity_score': 0.0,
            'confidence': 0.0,
            'risk_level': 'unknown',
            'recommendation': f"处理失败: {error_message}",
            'processing_time': processing_time,
            'subject': 'unknown',
            'question_type': 'unknown',
            'detailed_results': {},
            'metadata': {
                'error': True,
                'error_message': error_message
            }
        }
    
    def batch_compare(self, image_pairs: list, subject: str = 'general', question_type: str = 'general') -> list:
        """
        批量比较图片相似度
        
        Args:
            image_pairs: 图片对列表，每个元素为(image1, image2)
            subject: 学科类型
            question_type: 题型
            
        Returns:
            list: 比较结果列表
        """
        logger.info(f"开始批量相似度比对: {len(image_pairs)}对图片")
        
        results = []
        for i, (image1, image2) in enumerate(image_pairs):
            try:
                result = self.compare_two_answers(image1, image2, subject, question_type)
                result['pair_index'] = i
                results.append(result)
                logger.debug(f"第{i+1}对图片比对完成")
            except Exception as e:
                logger.error(f"第{i+1}对图片比对失败: {str(e)}")
                error_result = self._create_error_result(str(e), 0.0)
                error_result['pair_index'] = i
                results.append(error_result)
        
        logger.info(f"批量相似度比对完成: {len(results)}个结果")
        return results


# 便捷函数
def compare_two_answers(image1: Union[str, np.ndarray, Image.Image],
                       image2: Union[str, np.ndarray, Image.Image],
                       subject: str = 'general',
                       question_type: str = 'general',
                       config: dict = None) -> Dict[str, Any]:
    """
    比较两张答题图片的相似度（便捷函数）
    
    Args:
        image1: 第一张图片
        image2: 第二张图片
        subject: 学科类型
        question_type: 题型
        config: 配置字典
        
    Returns:
        dict: 相似度比对结果
    """
    comparator = SimilarityComparator(config)
    return comparator.compare_two_answers(image1, image2, subject, question_type)
