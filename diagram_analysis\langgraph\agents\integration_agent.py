"""
结果集成Agent

负责整合各Agent的处理结果，生成最终输出的Agent
"""

import time
import json
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage

from ..state_management import (
    DiagramAnalysisState, AgentReport, AgentType, ProcessingStep,
    state_manager
)
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("integration_agent", CONFIG)


class ResultIntegrationAgent:
    """结果集成Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化结果集成Agent
        
        Args:
            config: 配置字典
        """
        self.config = config or CONFIG.get('diagram_analysis', {})
        self.agent_type = AgentType.INTEGRATOR
        
        # 输出配置
        self.output_config = self.config.get('output', {})
        self.include_metadata = self.output_config.get('include_metadata', True)
        self.include_quality_metrics = self.output_config.get('include_quality_metrics', True)
        
    def process(self, state: DiagramAnalysisState) -> DiagramAnalysisState:
        """
        执行结果集成
        
        Args:
            state: 当前状态
            
        Returns:
            DiagramAnalysisState: 更新后的状态
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始结果集成: session_id={state['session_id']}")
            
            # 更新处理步骤
            state = state_manager.update_step(state, ProcessingStep.RESULT_INTEGRATION)
            
            # 收集所有处理结果
            integration_data = self._collect_processing_results(state)
            
            # 生成最终结果
            final_result = self._generate_final_result(state, integration_data)
            
            # 更新状态
            state = state_manager.set_final_result(state, final_result)
            
            # 生成Agent报告
            processing_time = time.time() - start_time
            report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.RESULT_INTEGRATION,
                success=True,
                processing_time=processing_time,
                message="结果集成完成",
                data={
                    "output_format": final_result.get('format', 'unknown'),
                    "has_mermaid": 'mermaid_code' in final_result,
                    "has_structured_data": 'structured_data' in final_result,
                    "has_description": 'description' in final_result
                }
            )
            
            state = state_manager.add_agent_report(state, report)
            
            # 添加消息到状态
            message = AIMessage(
                content=f"结果集成完成。生成了包含{len(final_result)}个字段的最终结果"
            )
            state["messages"].append(message)
            
            # 清除next_agent，表示工作流完成
            state["next_agent"] = None
            
            logger.info(f"结果集成成功: 耗时={processing_time:.2f}s")
            return state
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"结果集成失败: {str(e)}"
            logger.error(error_msg)
            
            # 记录错误报告
            error_report = AgentReport(
                agent_type=self.agent_type,
                step=ProcessingStep.RESULT_INTEGRATION,
                success=False,
                processing_time=processing_time,
                message="结果集成失败",
                error=str(e)
            )
            
            state = state_manager.add_agent_report(state, error_report)
            state = state_manager.set_error(state, error_msg, self.agent_type)
            
            return state
    
    def _collect_processing_results(self, state: DiagramAnalysisState) -> Dict[str, Any]:
        """收集所有处理结果"""
        try:
            integration_data = {
                'preprocessing': {},
                'classification': {},
                'extraction': {},
                'metadata': {}
            }
            
            # 收集预处理结果
            image_quality_report = state.get('image_quality_report')
            if image_quality_report:
                integration_data['preprocessing'] = {
                    'original_resolution': image_quality_report.resolution,
                    'quality_score': image_quality_report.quality_score,
                    'clarity_score': image_quality_report.clarity_score,
                    'contrast_score': image_quality_report.contrast_score,
                    'noise_level': image_quality_report.noise_level,
                    'has_text': image_quality_report.has_text,
                    'recommendations': image_quality_report.recommendations
                }
            
            # 收集分类结果
            classification_result = state.get('classification_result')
            if classification_result:
                integration_data['classification'] = {
                    'diagram_type': classification_result.diagram_type,
                    'confidence': classification_result.confidence,
                    'alternative_types': classification_result.alternative_types,
                    'reasoning': classification_result.reasoning,
                    'features_detected': classification_result.features_detected,
                    'validation_passed': classification_result.validation_passed
                }
            
            # 收集提取结果
            extraction_result = state.get('extraction_result')
            if extraction_result:
                integration_data['extraction'] = {
                    'mermaid_code': extraction_result.mermaid_code,
                    'mermaid_valid': extraction_result.mermaid_valid,
                    'structured_data': extraction_result.structured_data,
                    'text_description': extraction_result.text_description,
                    'extraction_confidence': extraction_result.extraction_confidence,
                    'elements_count': extraction_result.elements_count,
                    'relationships_count': extraction_result.relationships_count,
                    'quality_metrics': extraction_result.quality_metrics
                }
            
            # 收集元数据
            integration_data['metadata'] = {
                'session_id': state.get('session_id'),
                'processing_time': state.get('total_processing_time', 0.0),
                'request_params': state.get('request_params', {}),
                'agent_reports_count': len(state.get('agent_reports', [])),
                'error_count': len(state.get('error_messages', []))
            }
            
            return integration_data
            
        except Exception as e:
            logger.error(f"收集处理结果失败: {str(e)}")
            return {}
    
    def _generate_final_result(self, state: DiagramAnalysisState, integration_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终结果"""
        try:
            request_params = state.get('request_params', {})
            output_format = request_params.get('output_format', 'mermaid')
            
            # 基础结果结构
            final_result = {
                'success': True,
                'session_id': state.get('session_id'),
                'format': output_format,
                'timestamp': time.time()
            }
            
            # 添加主要结果
            extraction_data = integration_data.get('extraction', {})
            classification_data = integration_data.get('classification', {})
            
            # 图形类型信息
            final_result['diagram_type'] = classification_data.get('diagram_type', 'unknown')
            final_result['type_confidence'] = classification_data.get('confidence', 0.0)
            
            # 根据输出格式添加相应内容
            if output_format == 'mermaid' or output_format == 'all':
                final_result['mermaid_code'] = extraction_data.get('mermaid_code', '')
                final_result['mermaid_valid'] = extraction_data.get('mermaid_valid', False)
            
            if output_format == 'json' or output_format == 'all':
                final_result['structured_data'] = extraction_data.get('structured_data', {})
            
            if output_format == 'text' or output_format == 'all':
                final_result['description'] = extraction_data.get('text_description', '')
            
            # 添加质量指标
            if self.include_quality_metrics:
                final_result['quality_metrics'] = {
                    'extraction_confidence': extraction_data.get('extraction_confidence', 0.0),
                    'elements_count': extraction_data.get('elements_count', 0),
                    'relationships_count': extraction_data.get('relationships_count', 0),
                    'overall_quality': self._calculate_overall_quality(integration_data)
                }
            
            # 添加元数据
            if self.include_metadata:
                final_result['metadata'] = {
                    'processing_time': integration_data.get('metadata', {}).get('processing_time', 0.0),
                    'image_quality': integration_data.get('preprocessing', {}),
                    'classification_details': {
                        'reasoning': classification_data.get('reasoning', ''),
                        'alternative_types': classification_data.get('alternative_types', []),
                        'validation_passed': classification_data.get('validation_passed', False)
                    },
                    'processing_steps': self._get_processing_summary(state)
                }
            
            # 添加错误信息（如果有）
            error_messages = state.get('error_messages', [])
            if error_messages:
                final_result['warnings'] = error_messages
            
            return final_result
            
        except Exception as e:
            logger.error(f"生成最终结果失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'session_id': state.get('session_id'),
                'timestamp': time.time()
            }
    
    def _calculate_overall_quality(self, integration_data: Dict[str, Any]) -> float:
        """计算整体质量评分"""
        try:
            scores = []
            
            # 图像质量评分
            preprocessing_data = integration_data.get('preprocessing', {})
            if 'quality_score' in preprocessing_data:
                scores.append(preprocessing_data['quality_score'])
            
            # 分类置信度
            classification_data = integration_data.get('classification', {})
            if 'confidence' in classification_data:
                scores.append(classification_data['confidence'])
            
            # 提取置信度
            extraction_data = integration_data.get('extraction', {})
            if 'extraction_confidence' in extraction_data:
                scores.append(extraction_data['extraction_confidence'])
            
            # Mermaid有效性
            if extraction_data.get('mermaid_valid', False):
                scores.append(0.8)
            else:
                scores.append(0.3)
            
            # 计算加权平均
            if scores:
                return sum(scores) / len(scores)
            else:
                return 0.5  # 默认评分
                
        except Exception as e:
            logger.error(f"质量评分计算失败: {str(e)}")
            return 0.0
    
    def _get_processing_summary(self, state: DiagramAnalysisState) -> List[Dict[str, Any]]:
        """获取处理步骤摘要"""
        try:
            agent_reports = state.get('agent_reports', [])
            summary = []
            
            for report in agent_reports:
                step_info = {
                    'agent': report.agent_type.value,
                    'step': report.step.value,
                    'success': report.success,
                    'processing_time': report.processing_time,
                    'message': report.message
                }
                
                if report.error:
                    step_info['error'] = report.error
                
                summary.append(step_info)
            
            return summary
            
        except Exception as e:
            logger.error(f"处理摘要生成失败: {str(e)}")
            return []
    
    def format_for_api_response(self, final_result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化为API响应格式"""
        try:
            # 简化的API响应格式
            api_response = {
                'success': final_result.get('success', False),
                'data': {}
            }
            
            if final_result.get('success', False):
                # 成功响应
                api_response['data'] = {
                    'diagram_type': final_result.get('diagram_type'),
                    'mermaid_code': final_result.get('mermaid_code', ''),
                    'structured_data': final_result.get('structured_data', {}),
                    'description': final_result.get('description', ''),
                    'quality_score': final_result.get('quality_metrics', {}).get('overall_quality', 0.0)
                }
                
                # 可选的详细信息
                if self.include_metadata:
                    api_response['metadata'] = {
                        'processing_time': final_result.get('metadata', {}).get('processing_time', 0.0),
                        'session_id': final_result.get('session_id')
                    }
            else:
                # 错误响应
                api_response['error'] = final_result.get('error', 'Unknown error')
                api_response['session_id'] = final_result.get('session_id')
            
            return api_response
            
        except Exception as e:
            logger.error(f"API响应格式化失败: {str(e)}")
            return {
                'success': False,
                'error': f'Response formatting failed: {str(e)}'
            }
    
    def validate_final_result(self, final_result: Dict[str, Any]) -> tuple[bool, List[str]]:
        """验证最终结果的完整性"""
        try:
            errors = []
            
            # 检查必需字段
            required_fields = ['success', 'session_id', 'timestamp']
            for field in required_fields:
                if field not in final_result:
                    errors.append(f"Missing required field: {field}")
            
            # 如果成功，检查内容字段
            if final_result.get('success', False):
                if not final_result.get('diagram_type'):
                    errors.append("Missing diagram_type")
                
                # 检查至少有一种输出格式
                has_output = any([
                    final_result.get('mermaid_code'),
                    final_result.get('structured_data'),
                    final_result.get('description')
                ])
                
                if not has_output:
                    errors.append("No output content generated")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Validation error: {str(e)}"]


def create_integration_agent(config: Dict[str, Any] = None) -> ResultIntegrationAgent:
    """创建结果集成Agent"""
    return ResultIntegrationAgent(config)
