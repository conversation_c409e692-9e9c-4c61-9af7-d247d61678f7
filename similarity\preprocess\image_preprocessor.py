"""
图像预处理器

实现图像标准化、灰度转换、去噪、尺寸归一化等预处理功能
"""

import cv2
import numpy as np
from PIL import Image
import io
import base64
from typing import Union, Tuple, Optional
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

logger = setup_logger("image_preprocessor", CONFIG)


class ImagePreprocessor:
    """图像预处理器类"""
    
    def __init__(self, config: dict = None):
        """
        初始化图像预处理器
        
        Args:
            config: 预处理配置字典
        """
        self.config = config or CONFIG.get('similarity', {}).get('preprocessing', {})
        self.target_size = self.config.get('target_size', (800, 600))
        self.enable_normalize = self.config.get('normalize', True)
        self.enable_denoise = self.config.get('denoise', False)
        self.enable_sharpen = self.config.get('sharpen', False)
        self.enable_auto_rotate = self.config.get('auto_rotate', True)
        
    def preprocess_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        对图像进行完整的预处理流程
        
        Args:
            image_input: 输入图像，可以是base64字符串、numpy数组或PIL图像
            
        Returns:
            np.ndarray: 预处理后的图像数组
        """
        try:
            # 1. 加载图像
            image = self._load_image(image_input)
            
            # 2. 自动旋转校正
            if self.enable_auto_rotate:
                image = self._auto_rotate(image)
            
            # 3. 尺寸归一化
            image = self._resize_image(image)
            
            # 4. 转换为灰度图
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 5. 亮度和对比度标准化
            if self.enable_normalize:
                image = self._normalize_brightness_contrast(image)
            
            # 6. 降噪处理
            if self.enable_denoise:
                image = self._denoise(image)
            
            # 7. 锐化处理
            if self.enable_sharpen:
                image = self._sharpen(image)
            
            logger.debug(f"图像预处理完成，输出尺寸: {image.shape}")
            return image
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            raise
    
    def _load_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        加载图像到numpy数组
        
        Args:
            image_input: 输入图像
            
        Returns:
            np.ndarray: 图像数组
        """
        if isinstance(image_input, str):
            # base64字符串
            try:
                img_data = base64.b64decode(image_input)
                img = Image.open(io.BytesIO(img_data))
                return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            except Exception as e:
                logger.error(f"base64图像解码失败: {str(e)}")
                raise
                
        elif isinstance(image_input, Image.Image):
            # PIL图像
            return cv2.cvtColor(np.array(image_input), cv2.COLOR_RGB2BGR)
            
        elif isinstance(image_input, np.ndarray):
            # numpy数组
            return image_input.copy()
            
        else:
            raise ValueError(f"不支持的图像输入类型: {type(image_input)}")
    
    def _resize_image(self, image: np.ndarray) -> np.ndarray:
        """
        调整图像尺寸
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 调整尺寸后的图像
        """
        height, width = image.shape[:2]
        target_width, target_height = self.target_size
        
        # 计算缩放比例，保持宽高比
        scale = min(target_width / width, target_height / height)
        
        if scale < 1.0:  # 只有当图像过大时才缩放
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logger.debug(f"图像尺寸调整: {width}x{height} -> {new_width}x{new_height}")
        
        return image
    
    def _normalize_brightness_contrast(self, image: np.ndarray) -> np.ndarray:
        """
        标准化亮度和对比度
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 标准化后的图像
        """
        # 使用CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        normalized = clahe.apply(image)
        
        logger.debug("应用CLAHE亮度对比度标准化")
        return normalized
    
    def _denoise(self, image: np.ndarray) -> np.ndarray:
        """
        降噪处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 降噪后的图像
        """
        # 使用非局部均值降噪
        denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
        logger.debug("应用非局部均值降噪")
        return denoised
    
    def _sharpen(self, image: np.ndarray) -> np.ndarray:
        """
        锐化处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 锐化后的图像
        """
        # 使用拉普拉斯算子进行锐化
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]])
        sharpened = cv2.filter2D(image, -1, kernel)
        logger.debug("应用拉普拉斯锐化")
        return sharpened
    
    def _auto_rotate(self, image: np.ndarray) -> np.ndarray:
        """
        自动旋转校正
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 旋转校正后的图像
        """
        try:
            # 转换为灰度图进行文字检测
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 使用霍夫变换检测直线
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is not None and len(lines) > 0:
                # 计算主要角度
                angles = []
                for line in lines:
                    rho, theta = line[0]
                    angle = theta * 180 / np.pi
                    # 将角度转换到[-45, 45]范围
                    if angle > 45:
                        angle = angle - 90
                    elif angle < -45:
                        angle = angle + 90
                    angles.append(angle)
                
                # 使用中位数作为旋转角度
                if angles:
                    rotation_angle = np.median(angles)
                    
                    # 只有当角度超过阈值时才旋转
                    if abs(rotation_angle) > 1.0:
                        height, width = image.shape[:2]
                        center = (width // 2, height // 2)
                        rotation_matrix = cv2.getRotationMatrix2D(center, rotation_angle, 1.0)
                        rotated = cv2.warpAffine(image, rotation_matrix, (width, height), 
                                               flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
                        logger.debug(f"自动旋转校正: {rotation_angle:.2f}度")
                        return rotated
            
            return image
            
        except Exception as e:
            logger.warning(f"自动旋转校正失败: {str(e)}")
            return image
    
    def get_image_features(self, image: np.ndarray) -> dict:
        """
        提取图像基本特征信息
        
        Args:
            image: 输入图像
            
        Returns:
            dict: 图像特征信息
        """
        features = {
            'shape': image.shape,
            'dtype': str(image.dtype),
            'mean_intensity': float(np.mean(image)),
            'std_intensity': float(np.std(image)),
            'min_intensity': float(np.min(image)),
            'max_intensity': float(np.max(image))
        }
        
        if len(image.shape) == 2:  # 灰度图
            features['channels'] = 1
        else:
            features['channels'] = image.shape[2]
        
        return features
